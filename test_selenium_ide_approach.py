import time
import logging
import openpyxl
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.wait import WebDriverWait
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import NoSuchElementException

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_selenium_ide_approach():
    """Test using the Selenium IDE approach with specific element IDs"""
    
    # Load first student
    try:
        wb = openpyxl.load_workbook('student_list.xlsx')
        ws = wb.active
        first_row = list(ws.iter_rows(min_row=2, max_row=2, values_only=True))[0]
        student = {
            'id': str(first_row[0]),
            'name': first_row[1],
            'short_desc': first_row[6]
        }
        logger.info(f"Testing with: {student['id']} - {student['short_desc']}")
    except Exception as e:
        logger.error(f"Failed to load student data: {e}")
        return
    
    # Setup Chrome driver
    chrome_options = Options()
    chrome_options.add_argument("--start-maximized")
    driver = webdriver.Chrome(options=chrome_options)
    wait = WebDriverWait(driver, 10)
    
    try:
        # Navigate and search
        url = "https://lucas.lincoln.ac.nz/psc/ps/EMPLOYEE/SA/c/NUI_FRAMEWORK.PT_AGSTARTPAGE_NUI.GBL?CONTEXTIDPARAMS=TEMPLATE_ID%3aPTPPNAVCOL&scname=ADMN_RECORDS_AND_ENROLMENT&PanelCollapsible=Y&PTPPB_GROUPLET_ID=LU_RECORDS_AND_ENROLMENT&CRefName=LU_RECORDS_AND_ENROLMENT_TILE"
        driver.get(url)
        
        input("Please login and press Enter to continue...")
        
        # Switch to frame and search
        try:
            driver.switch_to.frame(0)
        except:
            pass
        
        search_field = wait.until(EC.element_to_be_clickable((By.ID, "STDNT_CAR_SRCH_EMPLID")))
        search_field.clear()
        search_field.send_keys(student['id'])
        
        search_btn = wait.until(EC.element_to_be_clickable((By.ID, "PTS_CFG_CL_WRK_PTS_SRCH_BTN")))
        search_btn.click()
        
        time.sleep(3)
        
        # Method 1: Try the Selenium IDE approach with specific IDs
        logger.info("Method 1: Trying Selenium IDE approach with specific element IDs")
        
        # The original Selenium IDE used: PTS_CFG_CL_RSLT_NUI_SRCH3$14$$1
        # Let's try different row numbers to find the correct one
        
        # First, let's see what elements exist with this pattern
        result_elements = []
        for i in range(20):  # Try first 20 possible row numbers
            element_id = f"PTS_CFG_CL_RSLT_NUI_SRCH3${i}$$1"
            try:
                element = driver.find_element(By.ID, element_id)
                result_elements.append((i, element))
                logger.info(f"Found element with ID: {element_id}")
            except NoSuchElementException:
                continue
        
        logger.info(f"Found {len(result_elements)} result elements with Selenium IDE pattern")
        
        # Check each element to see which one contains our target program
        matching_element = None
        for row_num, element in result_elements:
            try:
                # Get the text content of this element or its parent row
                element_text = element.text
                parent_text = ""
                
                try:
                    parent_row = element.find_element(By.XPATH, "./ancestor::tr[1]")
                    parent_text = parent_row.text
                except:
                    pass
                
                combined_text = f"{element_text} {parent_text}"
                logger.info(f"Row {row_num}: {combined_text[:100]}...")
                
                if student['short_desc'] in combined_text:
                    matching_element = element
                    logger.info(f"✓ Found matching element at row {row_num}")
                    break
                    
            except Exception as e:
                logger.warning(f"Error checking element {row_num}: {e}")
                continue
        
        if matching_element:
            try:
                logger.info("Clicking matching element using Selenium IDE approach...")
                matching_element.click()
                logger.info("✓ Successfully clicked using Selenium IDE approach!")
                time.sleep(3)
                
                # Check if page changed
                new_url = driver.current_url
                logger.info(f"New URL: {new_url}")
                return True
                
            except Exception as e:
                logger.error(f"Failed to click matching element: {e}")
        
        # Method 2: Try alternative ID patterns
        logger.info("Method 2: Trying alternative ID patterns")
        
        # Look for other possible patterns
        alternative_patterns = [
            "PTS_CFG_CL_RSLT_NUI_SRCH3${}$$0",
            "PTS_CFG_CL_RSLT_NUI_SRCH3${}$$2", 
            "PTS_CFG_CL_RSLT_{}$$1",
            "PTS_CFG_CL_RSLT${}"
        ]
        
        for pattern in alternative_patterns:
            logger.info(f"Trying pattern: {pattern}")
            found_elements = []
            
            for i in range(10):
                element_id = pattern.format(i)
                try:
                    element = driver.find_element(By.ID, element_id)
                    found_elements.append((i, element))
                    logger.info(f"  Found: {element_id}")
                except NoSuchElementException:
                    continue
            
            # Check if any of these elements contain our target
            for row_num, element in found_elements:
                try:
                    element_text = element.text
                    parent_text = ""
                    
                    try:
                        parent_row = element.find_element(By.XPATH, "./ancestor::tr[1]")
                        parent_text = parent_row.text
                    except:
                        pass
                    
                    combined_text = f"{element_text} {parent_text}"
                    
                    if student['short_desc'] in combined_text:
                        logger.info(f"✓ Found match with pattern {pattern} at row {row_num}")
                        element.click()
                        logger.info("✓ Successfully clicked!")
                        time.sleep(3)
                        return True
                        
                except Exception as e:
                    continue
        
        # Method 3: Fallback to our XPath approach
        logger.info("Method 3: Fallback to XPath approach")
        
        xpath_query = f"//*[contains(text(), '{student['short_desc']}')]"
        target_elements = driver.find_elements(By.XPATH, xpath_query)
        logger.info(f"Found {len(target_elements)} elements with XPath")
        
        for i, element in enumerate(target_elements):
            try:
                # Try to find parent row and click it
                parent_row = element.find_element(By.XPATH, "./ancestor::tr[1]")
                parent_row.click()
                logger.info(f"✓ XPath approach successful on element {i}")
                time.sleep(3)
                return True
            except:
                try:
                    element.click()
                    logger.info(f"✓ Direct element click successful on element {i}")
                    time.sleep(3)
                    return True
                except:
                    continue
        
        logger.error("All methods failed")
        return False
        
    except Exception as e:
        logger.error(f"Error during test: {e}")
        return False
        
    finally:
        input("Press Enter to close browser...")
        driver.quit()

if __name__ == "__main__":
    success = test_selenium_ide_approach()
    if success:
        print("\n✓ Found working approach!")
    else:
        print("\n✗ All approaches failed.")
