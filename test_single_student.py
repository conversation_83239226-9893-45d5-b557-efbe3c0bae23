import time
import logging
import openpyxl
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.wait import WebDriverWait
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_single_student():
    """Test automation with the first student from the Excel file"""
    
    # Load first student from Excel
    try:
        wb = openpyxl.load_workbook('student_list.xlsx')
        ws = wb.active
        
        # Get first student data (row 2)
        first_row = list(ws.iter_rows(min_row=2, max_row=2, values_only=True))[0]
        student = {
            'id': str(first_row[0]),
            'name': first_row[1],
            'short_desc': first_row[6]
        }
        
        logger.info(f"Testing with student: {student['id']} - {student['name']} - {student['short_desc']}")
        
    except Exception as e:
        logger.error(f"Failed to load student data: {e}")
        return
    
    # Setup Chrome driver
    chrome_options = Options()
    chrome_options.add_argument("--start-maximized")
    driver = webdriver.Chrome(options=chrome_options)
    wait = WebDriverWait(driver, 10)
    
    try:
        # Step 1: Navigate to URL
        url = "https://lucas.lincoln.ac.nz/psc/ps/EMPLOYEE/SA/c/NUI_FRAMEWORK.PT_AGSTARTPAGE_NUI.GBL?CONTEXTIDPARAMS=TEMPLATE_ID%3aPTPPNAVCOL&scname=ADMN_RECORDS_AND_ENROLMENT&PanelCollapsible=Y&PTPPB_GROUPLET_ID=LU_RECORDS_AND_ENROLMENT&CRefName=LU_RECORDS_AND_ENROLMENT_TILE"
        driver.get(url)
        logger.info("✓ Navigated to system URL")
        
        # Wait for user to manually login if needed
        input("Please login manually if required, then press Enter to continue...")
        
        # Step 2: Switch to frame and find search field
        try:
            driver.switch_to.frame(0)
            logger.info("✓ Switched to frame")
        except:
            logger.info("No frame to switch to")
        
        # Step 3: Enter student ID
        search_field = wait.until(EC.element_to_be_clickable((By.ID, "STDNT_CAR_SRCH_EMPLID")))
        search_field.clear()
        search_field.send_keys(student['id'])
        logger.info(f"✓ Entered student ID: {student['id']}")
        
        # Step 4: Click search button
        search_btn = wait.until(EC.element_to_be_clickable((By.ID, "PTS_CFG_CL_WRK_PTS_SRCH_BTN")))
        search_btn.click()
        logger.info("✓ Clicked search button")
        
        # Wait for results
        time.sleep(5)
        
        # Step 5: Handle search results using proven approach
        expected_short_desc = student['short_desc']
        logger.info(f"Step 5: Looking for search results containing: '{expected_short_desc}'")

        # Use the proven approach from archived automation
        result_rows = driver.find_elements(By.CSS_SELECTOR, "[id*='PTS_CFG_CL_RSLT_NUI_SRCH3']")
        logger.info(f"Found {len(result_rows)} result rows")

        click_success = False

        if len(result_rows) == 0:
            logger.warning("No search results found")

        elif len(result_rows) == 1:
            logger.info("Single result found, clicking it")
            try:
                result_rows[0].click()
                logger.info("✓ Successfully clicked single result")
                time.sleep(3)
                click_success = True
            except Exception as e:
                logger.error(f"Failed to click single result: {e}")

        else:
            logger.info(f"Multiple results found ({len(result_rows)}), searching for matching program")

            # Try to find the row with matching program description
            for i, row in enumerate(result_rows):
                try:
                    # Get the text content of the row to check for program match
                    row_text = row.text.upper()
                    expected_upper = expected_short_desc.upper()

                    print(f"Row {i+1}: {row_text}")

                    if expected_upper in row_text:
                        logger.info(f"✓ Found matching program in row {i+1}: {expected_short_desc}")
                        row.click()
                        logger.info("✓ Successfully clicked matching row")
                        time.sleep(3)
                        click_success = True
                        break

                except Exception as e:
                    logger.warning(f"Error checking row {i+1}: {str(e)}")
                    continue

            if not click_success:
                logger.warning(f"No automatic match found for: {expected_short_desc}")

        # Handle manual selection if automatic matching failed
        if not click_success:
            logger.warning(f"✗ No matching row found or click failed for program: {expected_short_desc}")

            # Fallback: show options to user
            print(f"\nCould not automatically find match for '{expected_short_desc}'")
            if len(result_rows) > 0:
                print(f"\nAvailable options:")
                for i, row in enumerate(result_rows):
                    try:
                        print(f"{i+1}. {row.text}")
                    except:
                        print(f"{i+1}. [Unable to read row text]")

                row_choice = input(f"Enter row number (1-{len(result_rows)}) or 's' to skip: ")

                if row_choice.lower() != 's':
                    try:
                        row_index = int(row_choice) - 1  # Convert to 0-based index
                        if 0 <= row_index < len(result_rows):
                            result_rows[row_index].click()
                            logger.info(f"✓ Manually selected row {row_index + 1}")
                            time.sleep(3)
                            click_success = True
                        else:
                            logger.error("Invalid row number")
                            return
                    except ValueError:
                        logger.error("Invalid input")
                        return
                else:
                    logger.info("Skipped row selection")
                    return
            else:
                logger.info("No rows available for manual selection")
                return

        if click_success:
            logger.info("✓ Row selection successful, proceeding...")
        
        # Continue with the rest of the process
        print("\nCurrent page URL:", driver.current_url)
        print("Page title:", driver.title)
        
        # Ask user if they want to continue with supervisor update
        continue_choice = input("\nDo you want to continue with supervisor update? (y/n): ")
        
        if continue_choice.lower() == 'y':
            try:
                # Step 1: Click the new record button FIRST
                print("\nStep 1: Looking for new record button...")
                try:
                    new_btn = wait.until(EC.element_to_be_clickable((By.ID, "SSR_ACDPRG_AUS$new$0$$0")))
                    new_btn.click()
                    time.sleep(2)
                    print("✓ Successfully clicked new record button")
                except Exception as e:
                    print(f"✗ Failed to click new record button: {e}")
                    return

                # Step 2: Now look for the form elements that should be available
                print("\nStep 2: Looking for form elements after clicking new...")
                elements_to_find = [
                    "ACAD_PROG_PROG_ACTION$0",
                    "ACAD_PROG_PROG_REASON$0",
                    "#ICSave"
                ]

                # Check for supervisor field with multiple possible IDs
                supervisor_field_ids = [
                    "LU_SUPLU_TBL_LU_SUPERVISOR$0",
                    "LU_SUP_LU_TBL_LU_SUPERVISOR$0",
                    "LU_SUPERVISOR$0",
                    "SUPERVISOR$0"
                ]

                supervisor_found = False
                for field_id in supervisor_field_ids:
                    try:
                        driver.find_element(By.ID, field_id)
                        print(f"✓ Found supervisor field: {field_id}")
                        supervisor_found = True
                        break
                    except NoSuchElementException:
                        continue

                if not supervisor_found:
                    print("✗ No supervisor field found with any known ID")

                for element_id in elements_to_find:
                    try:
                        driver.find_element(By.ID, element_id)
                        print(f"✓ Found element: {element_id}")
                    except NoSuchElementException:
                        print(f"✗ Element not found: {element_id}")

                # Step 3: Test the complete supervisor update process
                test_complete = input("\nDo you want to test the complete supervisor update process? (y/n): ")
                if test_complete.lower() == 'y':
                    try:
                        print("\nStep 3: Testing complete supervisor update...")

                        # Fill Program Action
                        prog_action = driver.find_element(By.ID, "ACAD_PROG_PROG_ACTION$0")
                        prog_action.clear()
                        prog_action.send_keys("Data")
                        print("✓ Entered 'Data' in Program Action")

                        # Fill Program Reason
                        prog_reason = driver.find_element(By.ID, "ACAD_PROG_PROG_REASON$0")
                        prog_reason.clear()
                        prog_reason.send_keys("PGRD")
                        print("✓ Entered 'PGRD' in Program Reason")

                        # Fill Supervisor field
                        supervisor_field = None
                        for field_id in supervisor_field_ids:
                            try:
                                supervisor_field = driver.find_element(By.ID, field_id)
                                print(f"✓ Found supervisor field: {field_id}")
                                break
                            except:
                                continue

                        if supervisor_field:
                            supervisor_field.clear()
                            supervisor_field.send_keys("8000401")
                            print("✓ Entered supervisor ID: 8000401")
                        else:
                            print("✗ Could not find supervisor field")

                        # Ask before saving
                        save_choice = input("\nDo you want to save these changes? (y/n): ")
                        if save_choice.lower() == 'y':
                            save_btn = driver.find_element(By.ID, "#ICSave")
                            save_btn.click()
                            print("✓ Clicked save button")
                            time.sleep(3)
                            print("✓ Save completed - check for any error messages on screen")
                        else:
                            print("Save skipped")

                    except Exception as e:
                        print(f"✗ Error during complete test: {e}")

            except Exception as e:
                logger.error(f"Error in supervisor update section: {e}")
        
    except Exception as e:
        logger.error(f"Error during automation: {e}")
        
    finally:
        input("Press Enter to close browser...")
        driver.quit()

if __name__ == "__main__":
    test_single_student()
