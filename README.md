# Student Supervisor Automation

This automation script helps update student supervisor information in the Lincoln University system by processing a list of students from an Excel file.

## Features

- Reads student data from Excel file
- Automates web browser interactions
- Handles multiple search results by matching program descriptions
- Provides detailed logging of all actions
- Allows manual intervention when errors occur
- Generates comprehensive reports

## Files

- `student_supervisor_automation.py` - Main automation script
- `test_single_student.py` - Test script for single student
- `setup.py` - Setup and installation script
- `requirements.txt` - Required Python packages
- `student_list.xlsx` - Student data (copied from "List to update.xlsx")

## Prerequisites

1. **Python 3.7+** installed
2. **Google Chrome browser** installed
3. **ChromeDriver** (will be handled automatically by webdriver-manager)
4. **Excel file** with student data

## Setup

1. Run the setup script:
   ```bash
   python setup.py
   ```

2. This will install required packages:
   - selenium (web automation)
   - openpyxl (Excel file reading)
   - webdriver-manager (ChromeDriver management)

## Excel File Format

The Excel file should have these columns:
- **ID**: Student ID
- **Name**: Student name
- **Career**: Career type (e.g., PGRD)
- **Eff Date**: Effective date
- **Status**: Status (e.g., AC)
- **Acad Prog**: Academic program code
- **Short Desc**: Program short description (used for matching)

## Usage

### Test with Single Student

First, test with one student to ensure everything works:

```bash
python test_single_student.py
```

This will:
1. Load the first student from the Excel file
2. Navigate to the system
3. Allow you to login manually
4. Show you the search results
5. Let you verify the process step by step

### Full Automation

Once testing is successful, run the full automation:

```bash
python student_supervisor_automation.py
```

## Automation Process

The script follows these steps for each student:

1. **Navigate** to the Lincoln University system URL
2. **Search** for student by ID in field `STDNT_CAR_SRCH_EMPLID`
3. **Click** search button `PTS_CFG_CL_WRK_PTS_SRCH_BTN`
4. **Handle results**:
   - If multiple results: Find row matching "Short Desc" from Excel
   - If single result: Continue automatically
   - If no match: Log error and ask user to skip or stop
5. **Click** result link `PTS_CFG_CL_RSLT_NUI_SRCH3$14$$1`
6. **Click** new record button `SSR_ACDPRG_AUS$new$0$$0`
7. **Fill** Program Action field `ACAD_PROG_PROG_ACTION$0` with "Data"
8. **Fill** Program Reason field `ACAD_PROG_PROG_REASON$0` with "PGRD"
9. **Fill** Supervisor field `LU_SUPLU_TBL_LU_SUPERVISOR$0` with "8000401"
10. **Click** save button `#ICSave`
11. **Check** for errors and handle accordingly

## Error Handling

The script includes comprehensive error handling:

- **Login required**: Pauses for manual login
- **Multiple search results**: Matches by program description
- **No matching program**: Logs error and asks user to skip/stop
- **Save errors**: Detects error messages and allows retry/manual fix
- **Network issues**: Implements waits and retries

## Logging

All actions are logged to:
- Console output (real-time)
- Log file: `automation_log_YYYYMMDD_HHMMSS.log`

Log includes:
- Timestamp for each action
- Success/failure status
- Error messages
- Student processing results

## Manual Intervention

When errors occur, the script will prompt:
- **(c)ontinue**: Skip current student and continue
- **(s)kip**: Skip all remaining students
- **(q)uit**: Stop automation
- **(r)etry**: Retry current operation
- **(m)anual**: Pause for manual fix

## Output Summary

At the end, you'll get a summary:
```
AUTOMATION SUMMARY
==================
Total processed: 149
Successful: 145
Failed: 4
Skipped: 0

Errors:
  - Failed to process 1002169: No matching program found
  - Failed to process 1042457: Save error occurred
```

## Troubleshooting

### Common Issues

1. **ChromeDriver not found**
   - Install Google Chrome
   - Run setup.py again

2. **Excel file permission denied**
   - Close Excel if file is open
   - Check file permissions

3. **Login timeout**
   - Login manually when prompted
   - Ensure you have proper access rights

4. **Element not found**
   - Page structure may have changed
   - Check if you're on the correct page
   - Verify element IDs in browser developer tools

### Debug Mode

For debugging, you can:
1. Add `time.sleep()` calls to slow down execution
2. Use `input()` to pause at specific points
3. Check browser developer tools for element IDs
4. Review log files for detailed error information

## Security Notes

- The script does not store login credentials
- All data is processed locally
- Log files may contain student information - handle securely
- Ensure compliance with data protection policies

## Support

If you encounter issues:
1. Check the log files for detailed error messages
2. Verify the Excel file format matches requirements
3. Test with a single student first
4. Ensure all prerequisites are installed correctly
