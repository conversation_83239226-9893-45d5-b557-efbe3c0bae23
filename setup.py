#!/usr/bin/env python3
"""
Setup script for Student Supervisor Automation
"""

import subprocess
import sys
import os

def install_requirements():
    """Install required packages"""
    try:
        print("Installing required packages...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✓ All packages installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ Failed to install packages: {e}")
        return False

def check_chromedriver():
    """Check if ChromeDriver is available"""
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        
        options = Options()
        options.add_argument("--headless")
        driver = webdriver.Chrome(options=options)
        driver.quit()
        print("✓ ChromeDriver is working")
        return True
    except Exception as e:
        print(f"✗ ChromeDriver issue: {e}")
        print("Please ensure Chrome browser and ChromeDriver are installed")
        return False

def main():
    print("Setting up Student Supervisor Automation...")
    print("=" * 50)
    
    # Install requirements
    if not install_requirements():
        return False
    
    # Check ChromeDriver
    if not check_chromedriver():
        print("\nNote: You may need to:")
        print("1. Install Google Chrome browser")
        print("2. Download ChromeDriver from https://chromedriver.chromium.org/")
        print("3. Add ChromeDriver to your PATH")
        return False
    
    print("\n✓ Setup completed successfully!")
    print("\nNext steps:")
    print("1. Run 'python test_single_student.py' to test with one student")
    print("2. Run 'python student_supervisor_automation.py' for full automation")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
