import time
import logging
import openpyxl
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.wait import WebDriverWait
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, WebDriverException

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'automation_log_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class StudentSupervisorAutomation:
    def __init__(self, excel_file='student_list.xlsx'):
        self.excel_file = excel_file
        self.driver = None
        self.wait = None
        self.results = {
            'processed': 0,
            'successful': 0,
            'failed': 0,
            'skipped': 0,
            'errors': []
        }
        
    def setup_driver(self):
        """Initialize Chrome driver with options"""
        try:
            chrome_options = Options()
            chrome_options.add_argument("--start-maximized")
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            
            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            self.wait = WebDriverWait(self.driver, 10)
            logger.info("Chrome driver initialized successfully")
            return True
        except Exception as e:
            logger.error(f"Failed to initialize Chrome driver: {e}")
            return False
    
    def load_student_data(self):
        """Load student data from Excel file"""
        try:
            wb = openpyxl.load_workbook(self.excel_file)
            ws = wb.active
            
            students = []
            headers = [cell.value for cell in ws[1]]
            logger.info(f"Excel headers: {headers}")
            
            for row in ws.iter_rows(min_row=2, values_only=True):
                if row[0]:  # Check if ID exists
                    student = {
                        'id': str(row[0]),
                        'name': row[1],
                        'career': row[2],
                        'eff_date': row[3],
                        'status': row[4],
                        'acad_prog': row[5],
                        'short_desc': row[6]
                    }
                    students.append(student)
            
            logger.info(f"Loaded {len(students)} students from Excel file")
            return students
        except Exception as e:
            logger.error(f"Failed to load student data: {e}")
            return []
    
    def navigate_to_system(self):
        """Navigate to the student system"""
        try:
            url = "https://lucas.lincoln.ac.nz/psc/ps/EMPLOYEE/SA/c/NUI_FRAMEWORK.PT_AGSTARTPAGE_NUI.GBL?CONTEXTIDPARAMS=TEMPLATE_ID%3aPTPPNAVCOL&scname=ADMN_RECORDS_AND_ENROLMENT&PanelCollapsible=Y&PTPPB_GROUPLET_ID=LU_RECORDS_AND_ENROLMENT&CRefName=LU_RECORDS_AND_ENROLMENT_TILE"
            self.driver.get(url)
            logger.info("Navigated to student system")
            
            # Wait for page to load
            time.sleep(3)
            return True
        except Exception as e:
            logger.error(f"Failed to navigate to system: {e}")
            return False
    
    def search_student(self, student_id):
        """Search for a student by ID"""
        try:
            # Switch to frame if needed
            try:
                self.driver.switch_to.frame(0)
            except:
                pass

            # Clear and enter student ID
            search_field = self.wait.until(EC.element_to_be_clickable((By.ID, "STDNT_CAR_SRCH_EMPLID")))
            search_field.clear()
            search_field.send_keys(student_id)
            logger.info(f"Entered student ID: {student_id}")

            # Click search button
            search_btn = self.wait.until(EC.element_to_be_clickable((By.ID, "PTS_CFG_CL_WRK_PTS_SRCH_BTN")))
            search_btn.click()
            logger.info("Clicked search button")

            # Wait for results
            time.sleep(3)
            return True
        except Exception as e:
            logger.error(f"Failed to search for student {student_id}: {e}")
            return False

    def find_program_column(self, header_row):
        """Find which column contains the program description"""
        try:
            cells = header_row.find_elements(By.TAG_NAME, "th")
            for i, cell in enumerate(cells):
                cell_text = cell.text.strip().lower()
                # Look for common program description column names
                if any(keyword in cell_text for keyword in ['program', 'descr', 'description', 'short']):
                    logger.info(f"Found program column at index {i}: {cell.text}")
                    return i

            # If no specific column found, try common positions
            logger.warning("Could not identify program column, using fallback positions")
            return None
        except Exception as e:
            logger.warning(f"Error finding program column: {e}")
            return None
    
    def handle_search_results(self, expected_short_desc):
        """Handle search results and select the correct program"""
        try:
            # Use the proven approach from archived automation
            logger.info("Using proven CSS selector approach for search results")

            # Find all result rows using the selector from the working automation
            result_rows = self.driver.find_elements(By.CSS_SELECTOR, "[id*='PTS_CFG_CL_RSLT_NUI_SRCH3']")
            logger.info(f"Found {len(result_rows)} result rows")

            if len(result_rows) == 0:
                logger.warning("No search results found")
                return False

            elif len(result_rows) == 1:
                logger.info("Single result found, clicking it")
                result_rows[0].click()
                time.sleep(2)
                return True

            else:
                logger.info(f"Multiple results found ({len(result_rows)}), searching for matching program")

                # Try to find the row with matching program description
                for i, row in enumerate(result_rows):
                    try:
                        # Get the text content of the row to check for program match
                        row_text = row.text.upper()
                        expected_upper = expected_short_desc.upper()

                        logger.info(f"Checking row {i+1}: {row_text[:100]}...")

                        if expected_upper in row_text:
                            logger.info(f"✓ Found matching program in row {i+1}: {expected_short_desc}")
                            row.click()
                            time.sleep(2)
                            return True

                    except Exception as e:
                        logger.warning(f"Error checking row {i+1}: {str(e)}")
                        continue

                # If no automatic match found, log available options
                logger.warning(f"No automatic match found for: {expected_short_desc}")
                logger.info("Available options:")
                for i, row in enumerate(result_rows):
                    try:
                        logger.info(f"  Row {i+1}: {row.text[:100]}...")
                    except:
                        logger.info(f"  Row {i+1}: [Unable to read row text]")

                return False

        except Exception as e:
            logger.error(f"Failed to handle search results: {e}")
            return False
    
    def update_supervisor(self):
        """Update the supervisor information"""
        try:
            # Note: Row selection is now handled in handle_search_results()
            # We should already be on the correct page after row selection
            logger.info("Starting supervisor update process...")

            # Step 9: Click new record button (REQUIRED before filling data)
            logger.info("Looking for new record button...")
            new_btn = self.wait.until(EC.element_to_be_clickable((By.ID, "SSR_ACDPRG_AUS$new$0$$0")))
            new_btn.click()
            time.sleep(2)
            logger.info("✓ Clicked new record button - ready to fill data")

            # Step 10: Click Program Action field
            prog_action = self.wait.until(EC.element_to_be_clickable((By.ID, "ACAD_PROG_PROG_ACTION$0")))
            prog_action.click()
            time.sleep(1)

            # Step 11: Type "Data" in Program Action
            prog_action.clear()
            prog_action.send_keys("Data")
            logger.info("✓ Entered 'Data' in Program Action")

            # Step 12: Type "PGRD" in Program Reason
            prog_reason = self.driver.find_element(By.ID, "ACAD_PROG_PROG_REASON$0")
            prog_reason.clear()
            prog_reason.send_keys("PGRD")
            logger.info("✓ Entered 'PGRD' in Program Reason")

            # Step 13: Click supervisor field (try multiple possible IDs)
            supervisor_field = None
            supervisor_field_ids = [
                "LU_SUPLU_TBL_LU_SUPERVISOR$0",
                "LU_SUP_LU_TBL_LU_SUPERVISOR$0",
                "LU_SUPERVISOR$0",
                "SUPERVISOR$0"
            ]

            for field_id in supervisor_field_ids:
                try:
                    supervisor_field = self.wait.until(EC.element_to_be_clickable((By.ID, field_id)))
                    logger.info(f"✓ Found supervisor field with ID: {field_id}")
                    break
                except:
                    logger.debug(f"Supervisor field ID not found: {field_id}")
                    continue

            if not supervisor_field:
                logger.error("Could not find supervisor field with any known ID")
                return False, "Supervisor field not found"

            supervisor_field.click()
            time.sleep(1)

            # Step 14: Type supervisor ID "8000401"
            supervisor_field.clear()
            supervisor_field.send_keys("8000401")
            logger.info("✓ Entered supervisor ID: 8000401")

            # Step 15: Click save button
            save_btn = self.wait.until(EC.element_to_be_clickable((By.ID, "#ICSave")))
            save_btn.click()
            logger.info("✓ Clicked save button")

            # Wait for page to reload and check for errors
            time.sleep(5)

            # Check for error messages or success indicators
            try:
                error_elements = self.driver.find_elements(By.CSS_SELECTOR, ".PSERROR, .PSWARNING, .PSMSGDISPLAY")
                if error_elements:
                    error_text = " | ".join([elem.text for elem in error_elements if elem.text.strip()])
                    logger.error(f"Error after save: {error_text}")

                    # Prompt user for manual intervention
                    user_action = input(f"\nError occurred: {error_text}\nChoose: (r)etry, (s)kip, (m)anual fix: ").lower()
                    if user_action == 'r':
                        return self.update_supervisor()  # Retry
                    elif user_action == 'm':
                        input("Please fix manually, then press Enter to continue...")
                        return True, "Manual fix applied"
                    else:
                        return False, error_text
            except:
                pass

            logger.info("✓ Supervisor updated successfully")
            return True, "Success"

        except Exception as e:
            logger.error(f"Failed to update supervisor: {e}")
            # Prompt user for manual intervention
            user_action = input(f"\nException occurred: {e}\nChoose: (r)etry, (s)kip, (m)anual fix: ").lower()
            if user_action == 'r':
                return self.update_supervisor()  # Retry
            elif user_action == 'm':
                input("Please fix manually, then press Enter to continue...")
                return True, "Manual fix applied"
            else:
                return False, str(e)
    
    def process_student(self, student):
        """Process a single student"""
        student_id = student['id']
        short_desc = student['short_desc']
        
        logger.info(f"Processing student: {student_id} - {student['name']} - {short_desc}")
        
        try:
            # Navigate to system
            if not self.navigate_to_system():
                return False, "Failed to navigate to system"
            
            # Search for student
            if not self.search_student(student_id):
                return False, "Failed to search for student"
            
            # Handle search results
            if not self.handle_search_results(short_desc):
                return False, f"No matching program found for {short_desc}"
            
            # Update supervisor
            success, message = self.update_supervisor()
            return success, message
            
        except Exception as e:
            logger.error(f"Error processing student {student_id}: {e}")
            return False, str(e)
    
    def run_automation(self):
        """Main automation function"""
        logger.info("Starting student supervisor automation")
        
        # Setup driver
        if not self.setup_driver():
            return
        
        try:
            # Load student data
            students = self.load_student_data()
            if not students:
                logger.error("No student data loaded")
                return
            
            # Process each student
            for i, student in enumerate(students):
                self.results['processed'] += 1
                
                logger.info(f"Processing student {i+1}/{len(students)}: {student['id']}")
                
                success, message = self.process_student(student)
                
                if success:
                    self.results['successful'] += 1
                    logger.info(f"✓ Successfully processed {student['id']}")
                else:
                    self.results['failed'] += 1
                    error_msg = f"✗ Failed to process {student['id']}: {message}"
                    logger.error(error_msg)
                    self.results['errors'].append(error_msg)
                    
                    # Ask user what to do on failure
                    user_choice = input(f"\nFailed to process {student['id']} - {student['name']}\nError: {message}\nChoose: (c)ontinue, (s)kip remaining, (q)uit: ").lower()
                    
                    if user_choice == 's':
                        self.results['skipped'] = len(students) - i - 1
                        logger.info(f"Skipping remaining {len(students) - i - 1} students")
                        break
                    elif user_choice == 'q':
                        logger.info("User chose to quit")
                        break
                
                # Small delay between students
                time.sleep(2)
            
        finally:
            # Cleanup
            if self.driver:
                self.driver.quit()
            
            # Print summary
            self.print_summary()
    
    def print_summary(self):
        """Print automation summary"""
        logger.info("\n" + "="*50)
        logger.info("AUTOMATION SUMMARY")
        logger.info("="*50)
        logger.info(f"Total processed: {self.results['processed']}")
        logger.info(f"Successful: {self.results['successful']}")
        logger.info(f"Failed: {self.results['failed']}")
        logger.info(f"Skipped: {self.results['skipped']}")
        
        if self.results['errors']:
            logger.info("\nErrors:")
            for error in self.results['errors']:
                logger.info(f"  - {error}")

if __name__ == "__main__":
    automation = StudentSupervisorAutomation()
    automation.run_automation()
