"""
Student Supervisor Automation System

This module automates the process of updating student supervisor information
in the Lincoln University student management system.

Author: Tank
Date: 2025-08-05
Version: 2.0.0
"""

import time
import logging
import openpyxl
import json
import traceback
from datetime import datetime
from pathlib import Path
from typing import List, Tuple, Optional
from dataclasses import dataclass
from contextlib import contextmanager

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.wait import WebDriverWait
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException

@dataclass
class StudentRecord:
    """Data class for student information"""
    id: str
    name: str
    career: str
    eff_date: str
    status: str
    acad_prog: str
    short_desc: str

    def __post_init__(self):
        """Validate and clean data after initialization"""
        self.id = str(self.id).strip()
        self.name = str(self.name).strip() if self.name else ""
        self.short_desc = str(self.short_desc).strip() if self.short_desc else ""

@dataclass
class ProcessingResult:
    """Data class for processing results"""
    success: bool
    message: str
    student_id: str
    timestamp: datetime
    error_details: Optional[str] = None

class AutomationLogger:
    """Enhanced logging configuration for the automation system"""

    def __init__(self, log_level: str = "INFO"):
        self.log_dir = Path("logs")
        self.log_dir.mkdir(exist_ok=True)

        # Create timestamped log file
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.log_file = self.log_dir / f"automation_{timestamp}.log"
        self.error_log_file = self.log_dir / f"automation_errors_{timestamp}.log"

        # Configure main logger
        self.logger = logging.getLogger("StudentAutomation")
        self.logger.setLevel(getattr(logging, log_level.upper()))

        # Clear existing handlers
        self.logger.handlers.clear()

        # Create formatters
        detailed_formatter = logging.Formatter(
            '%(asctime)s | %(levelname)-8s | %(funcName)-20s | Line %(lineno)-4d | %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )

        console_formatter = logging.Formatter(
            '%(asctime)s | %(levelname)-8s | %(message)s',
            datefmt='%H:%M:%S'
        )

        # File handler for all logs
        file_handler = logging.FileHandler(self.log_file, encoding='utf-8')
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(detailed_formatter)

        # File handler for errors only
        error_handler = logging.FileHandler(self.error_log_file, encoding='utf-8')
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(detailed_formatter)

        # Console handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(console_formatter)

        # Add handlers
        self.logger.addHandler(file_handler)
        self.logger.addHandler(error_handler)
        self.logger.addHandler(console_handler)

        # Log startup information
        self.logger.info("="*80)
        self.logger.info("STUDENT SUPERVISOR AUTOMATION SYSTEM STARTED")
        self.logger.info("="*80)
        self.logger.info(f"Log file: {self.log_file}")
        self.logger.info(f"Error log: {self.error_log_file}")
        self.logger.info(f"Log level: {log_level}")

    def get_logger(self) -> logging.Logger:
        """Get the configured logger instance"""
        return self.logger

class StudentSupervisorAutomation:
    """
    Main automation class for updating student supervisor information.

    This class handles the complete workflow of:
    1. Loading student data from Excel
    2. Navigating to the student management system
    3. Searching for students and updating supervisor information
    4. Logging all activities and results
    """

    # Configuration constants
    DEFAULT_SUPERVISOR_ID = "8000401"
    PROGRAM_ACTION = "Data"
    PROGRAM_REASON = "PGRD"
    DEFAULT_TIMEOUT = 10
    SYSTEM_URL = "https://lucas.lincoln.ac.nz/psc/ps/EMPLOYEE/SA/c/NUI_FRAMEWORK.PT_AGSTARTPAGE_NUI.GBL?CONTEXTIDPARAMS=TEMPLATE_ID%3aPTPPNAVCOL&scname=ADMN_RECORDS_AND_ENROLMENT&PanelCollapsible=Y&PTPPB_GROUPLET_ID=LU_RECORDS_AND_ENROLMENT&CRefName=LU_RECORDS_AND_ENROLMENT_TILE"

    def __init__(self,
                 excel_file: str = 'student_list.xlsx',
                 supervisor_id: str = None,
                 timeout: int = None,
                 log_level: str = "INFO"):
        """
        Initialize the automation system.

        Args:
            excel_file: Path to the Excel file containing student data
            supervisor_id: Supervisor ID to assign (defaults to DEFAULT_SUPERVISOR_ID)
            timeout: Selenium timeout in seconds (defaults to DEFAULT_TIMEOUT)
            log_level: Logging level (DEBUG, INFO, WARNING, ERROR)
        """
        # Initialize logging first
        self.automation_logger = AutomationLogger(log_level)
        self.logger = self.automation_logger.get_logger()

        # Configuration
        self.excel_file = Path(excel_file)
        self.supervisor_id = supervisor_id or self.DEFAULT_SUPERVISOR_ID
        self.timeout = timeout or self.DEFAULT_TIMEOUT

        # Selenium components
        self.driver: Optional[webdriver.Chrome] = None
        self.wait: Optional[WebDriverWait] = None

        # Results tracking
        self.results = {
            'processed': 0,
            'successful': 0,
            'failed': 0,
            'skipped': 0,
            'errors': [],
            'processing_results': []
        }

        # Validation
        self._validate_configuration()

        self.logger.info(f"Automation initialized with Excel file: {self.excel_file}")
        self.logger.info(f"Supervisor ID: {self.supervisor_id}")
        self.logger.info(f"Timeout: {self.timeout} seconds")

    def _validate_configuration(self) -> None:
        """Validate the configuration and input files"""
        if not self.excel_file.exists():
            raise FileNotFoundError(f"Excel file not found: {self.excel_file}")

        if not self.supervisor_id:
            raise ValueError("Supervisor ID cannot be empty")

        self.logger.debug("Configuration validation passed")

    @contextmanager
    def driver_context(self):
        """Context manager for WebDriver lifecycle management"""
        try:
            if not self.setup_driver():
                raise RuntimeError("Failed to initialize WebDriver")
            yield self.driver
        finally:
            self.cleanup_driver()

    def setup_driver(self) -> bool:
        """
        Initialize Chrome driver with enhanced options and error handling.

        Returns:
            bool: True if driver setup successful, False otherwise
        """
        try:
            self.logger.info("Initializing Chrome WebDriver...")

            # Configure Chrome options for automation
            chrome_options = Options()
            chrome_options.add_argument("--start-maximized")
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_argument("--disable-extensions")
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-gpu")
            chrome_options.add_argument("--remote-debugging-port=9222")
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)

            # Add user agent to appear more like a regular browser
            chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")

            # Initialize driver with automatic driver management
            try:
                # Try to use webdriver-manager for automatic driver management
                from webdriver_manager.chrome import ChromeDriverManager
                service = webdriver.chrome.service.Service(ChromeDriverManager().install())
                self.driver = webdriver.Chrome(service=service, options=chrome_options)
                self.logger.info("Using webdriver-manager for Chrome driver")
            except ImportError:
                # Fallback to system Chrome driver
                self.driver = webdriver.Chrome(options=chrome_options)
                self.logger.info("Using system Chrome driver")

            # Execute script to hide webdriver property
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

            # Initialize WebDriverWait
            self.wait = WebDriverWait(self.driver, self.timeout)

            # Set implicit wait
            self.driver.implicitly_wait(5)

            self.logger.info("✓ Chrome driver initialized successfully")
            self.logger.debug(f"Driver session ID: {self.driver.session_id}")

            return True

        except Exception as e:
            self.logger.error(f"✗ Failed to initialize Chrome driver: {e}")
            self.logger.debug(f"Driver setup error details: {traceback.format_exc()}")
            return False

    def cleanup_driver(self) -> None:
        """Safely cleanup the WebDriver instance"""
        if self.driver:
            try:
                self.logger.info("Cleaning up WebDriver...")
                self.driver.quit()
                self.logger.debug("✓ WebDriver cleanup completed")
            except Exception as e:
                self.logger.warning(f"Error during driver cleanup: {e}")
            finally:
                self.driver = None
                self.wait = None

    def load_student_data(self) -> List[StudentRecord]:
        """
        Load student data from Excel file and validate the structure.

        Returns:
            List[StudentRecord]: List of validated student records

        Raises:
            FileNotFoundError: If Excel file doesn't exist
            ValueError: If Excel file has invalid structure
        """
        try:
            self.logger.info(f"Loading student data from: {self.excel_file}")

            # Load workbook
            wb = openpyxl.load_workbook(self.excel_file)
            ws = wb.active

            # Get and validate headers
            headers = [cell.value for cell in ws[1]]
            self.logger.info(f"Excel headers: {headers}")

            # Expected columns: id, name, career, eff_date, status, acad_prog, short_desc

            students = []
            processed_count = 0
            error_count = 0

            for row_num, row in enumerate(ws.iter_rows(min_row=2, values_only=True), start=2):
                try:
                    if not row[0]:  # Skip empty rows
                        continue

                    processed_count += 1

                    # Create student record with validation
                    student = StudentRecord(
                        id=str(row[0]) if row[0] else "",
                        name=str(row[1]) if row[1] else "",
                        career=str(row[2]) if row[2] else "",
                        eff_date=str(row[3]) if row[3] else "",
                        status=str(row[4]) if row[4] else "",
                        acad_prog=str(row[5]) if row[5] else "",
                        short_desc=str(row[6]) if row[6] else ""
                    )

                    # Validate required fields
                    if not student.id:
                        self.logger.warning(f"Row {row_num}: Missing student ID, skipping")
                        error_count += 1
                        continue

                    if not student.short_desc:
                        self.logger.warning(f"Row {row_num}: Missing program description for student {student.id}")

                    students.append(student)
                    self.logger.debug(f"Loaded student: {student.id} - {student.name}")

                except Exception as e:
                    error_count += 1
                    self.logger.error(f"Error processing row {row_num}: {e}")
                    continue

            self.logger.info(f"✓ Successfully loaded {len(students)} students from Excel file")
            self.logger.info(f"Processed {processed_count} rows, {error_count} errors")

            if not students:
                raise ValueError("No valid student records found in Excel file")

            return students

        except FileNotFoundError:
            self.logger.error(f"✗ Excel file not found: {self.excel_file}")
            raise
        except Exception as e:
            self.logger.error(f"✗ Failed to load student data: {e}")
            self.logger.debug(f"Load data error details: {traceback.format_exc()}")
            raise

    def navigate_to_system(self) -> bool:
        """
        Navigate to the student management system and handle sign-in.

        Returns:
            bool: True if navigation successful, False otherwise
        """
        try:
            self.logger.info("Navigating to student management system...")

            # Navigate to the system URL
            self.driver.get(self.SYSTEM_URL)
            self.logger.debug(f"Navigated to URL: {self.SYSTEM_URL}")

            # Wait for page to load completely
            self.wait.until(EC.presence_of_element_located((By.TAG_NAME, "body")))

            # Check if we're on a sign-in page
            if self._is_sign_in_page():
                self.logger.info("Sign-in page detected - waiting for user intervention")
                if not self._handle_sign_in_page():
                    return False

            # After sign-in, we might need to navigate to Records and Enrolment
            if not self._ensure_records_and_enrolment_page():
                return False

            # Additional wait for dynamic content
            time.sleep(3)

            self.logger.info("✓ Successfully navigated to student system")
            return True

        except TimeoutException:
            self.logger.error("✗ Timeout while navigating to system")
            return False
        except Exception as e:
            self.logger.error(f"✗ Failed to navigate to system: {e}")
            self.logger.debug(f"Navigation error details: {traceback.format_exc()}")
            return False

    def _is_sign_in_page(self) -> bool:
        """
        Check if the current page is a sign-in page.

        Returns:
            bool: True if on sign-in page, False otherwise
        """
        try:
            # Check for common sign-in page elements
            sign_in_indicators = [
                (By.ID, "passwd"),  # Password field
                (By.ID, "login"),   # Login field
                (By.ID, "nsg-x1-logon-button"),  # Login button
                (By.XPATH, "//input[@type='password']"),  # Any password input
                (By.XPATH, "//*[contains(text(), 'Sign in') or contains(text(), 'Login') or contains(text(), 'Log in')]")
            ]

            for locator in sign_in_indicators:
                try:
                    element = self.driver.find_element(*locator)
                    if element.is_displayed():
                        self.logger.debug(f"Sign-in indicator found: {locator}")
                        return True
                except:
                    continue

            return False

        except Exception as e:
            self.logger.debug(f"Error checking for sign-in page: {e}")
            return False

    def _handle_sign_in_page(self) -> bool:
        """
        Handle sign-in page by waiting for user intervention.

        Returns:
            bool: True if sign-in successful, False otherwise
        """
        try:
            self.logger.warning("="*60)
            self.logger.warning("SIGN-IN REQUIRED")
            self.logger.warning("="*60)
            self.logger.warning("Please sign in to the system manually in the browser window.")
            self.logger.warning("After signing in and reaching the main system page, press Enter to continue...")

            # Wait for user to complete sign-in
            input("\nPress Enter after you have successfully signed in to the system: ")

            # Wait a moment for page to load after sign-in
            time.sleep(3)

            # Verify we're no longer on sign-in page
            if self._is_sign_in_page():
                self.logger.error("Still on sign-in page. Please ensure you have signed in successfully.")
                retry = input("Try again? (y/n): ").lower().strip()
                if retry == 'y':
                    return self._handle_sign_in_page()
                else:
                    return False

            self.logger.info("✓ Sign-in completed successfully")
            return True

        except KeyboardInterrupt:
            self.logger.warning("Sign-in interrupted by user")
            return False
        except Exception as e:
            self.logger.error(f"Error handling sign-in page: {e}")
            return False

    def _ensure_records_and_enrolment_page(self) -> bool:
        """
        Ensure we're on the Records and Enrolment page and switch to the correct frame.

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            self.logger.debug("Ensuring we're on Records and Enrolment page...")

            # Check if we need to click on Records and Enrolment link
            try:
                records_link = self.driver.find_element(By.ID, "LU_RECORDS_AND_ENROLMENT$3")
                if records_link.is_displayed():
                    self.logger.debug("Found Records and Enrolment link, clicking it...")
                    records_link.click()
                    time.sleep(3)
            except:
                self.logger.debug("Records and Enrolment link not found or not needed")

            # Try to switch to the main frame (usually frame 0)
            try:
                self.driver.switch_to.frame(0)
                self.logger.debug("✓ Switched to main frame")
            except Exception as e:
                self.logger.debug(f"Could not switch to frame: {e}")
                # Continue anyway, might not need frame switching

            # Verify we can find the student search field (indicates we're on the right page)
            try:
                search_field = self.driver.find_element(By.ID, "STDNT_CAR_SRCH_EMPLID")
                if search_field.is_displayed():
                    self.logger.debug("✓ Student search field found - on correct page")
                    return True
            except:
                pass

            # If we can't find the search field, ask user to navigate manually
            self.logger.warning("Could not automatically navigate to Records and Enrolment page")
            user_action = input(
                "\nPlease manually navigate to the Records and Enrolment page in the browser.\n"
                "Look for the student search field and ensure you're on the correct page.\n"
                "Press Enter when ready to continue: "
            )

            # Try again to find the search field
            try:
                search_field = self.driver.find_element(By.ID, "STDNT_CAR_SRCH_EMPLID")
                if search_field.is_displayed():
                    self.logger.info("✓ User navigated to correct page")
                    return True
            except:
                pass

            self.logger.error("Still cannot find student search field")
            return False

        except Exception as e:
            self.logger.error(f"Error ensuring Records and Enrolment page: {e}")
            return False

    def search_student(self, student_id: str) -> bool:
        """
        Search for a student by ID in the system.

        Args:
            student_id: The student ID to search for

        Returns:
            bool: True if search successful, False otherwise
        """
        try:
            self.logger.info(f"Searching for student: {student_id}")

            # Handle frame switching if needed
            try:
                self.driver.switch_to.frame(0)
                self.logger.debug("Switched to frame 0")
            except Exception:
                self.logger.debug("No frame switching needed")
                pass

            # Wait for and interact with search field
            search_field = self.wait.until(
                EC.element_to_be_clickable((By.ID, "STDNT_CAR_SRCH_EMPLID"))
            )

            # Clear and enter student ID
            search_field.clear()
            search_field.send_keys(student_id)
            self.logger.debug(f"Entered student ID: {student_id}")

            # Click search button
            search_btn = self.wait.until(
                EC.element_to_be_clickable((By.ID, "PTS_CFG_CL_WRK_PTS_SRCH_BTN"))
            )
            search_btn.click()
            self.logger.debug("Clicked search button")

            # Wait for search results to load
            time.sleep(3)

            self.logger.info(f"✓ Search completed for student: {student_id}")
            return True

        except TimeoutException:
            self.logger.error(f"✗ Timeout while searching for student {student_id}")
            return False
        except Exception as e:
            self.logger.error(f"✗ Failed to search for student {student_id}: {e}")
            self.logger.debug(f"Search error details: {traceback.format_exc()}")
            return False

    def find_program_column(self, header_row) -> Optional[int]:
        """
        Find which column contains the program description.

        Args:
            header_row: The header row element containing column headers

        Returns:
            Optional[int]: Column index if found, None otherwise
        """
        try:
            cells = header_row.find_elements(By.TAG_NAME, "th")
            for i, cell in enumerate(cells):
                cell_text = cell.text.strip().lower()
                # Look for common program description column names
                if any(keyword in cell_text for keyword in ['program', 'descr', 'description', 'short']):
                    self.logger.info(f"Found program column at index {i}: {cell.text}")
                    return i

            # If no specific column found, try common positions
            self.logger.warning("Could not identify program column, using fallback positions")
            return None
        except Exception as e:
            self.logger.warning(f"Error finding program column: {e}")
            return None

    def handle_search_results(self, expected_short_desc: str) -> bool:
        """
        Handle search results and select the correct program.

        Args:
            expected_short_desc: Expected program description to match

        Returns:
            bool: True if correct program selected, False otherwise
        """
        try:
            self.logger.info("Processing search results...")
            self.logger.debug(f"Looking for program: {expected_short_desc}")

            # Find all result rows using the proven CSS selector
            result_rows = self.driver.find_elements(By.CSS_SELECTOR, "[id*='PTS_CFG_CL_RSLT_NUI_SRCH3']")
            self.logger.info(f"Found {len(result_rows)} result rows")

            if len(result_rows) == 0:
                self.logger.warning("✗ No search results found")
                return False

            elif len(result_rows) == 1:
                self.logger.info("Single result found, selecting it")
                result_rows[0].click()
                time.sleep(2)
                self.logger.info("✓ Single result selected")
                return True

            else:
                self.logger.info(f"Multiple results found ({len(result_rows)}), searching for matching program")

                # Try to find the row with matching program description
                for i, row in enumerate(result_rows):
                    try:
                        # Get the text content of the row to check for program match
                        row_text = row.text.upper()
                        expected_upper = expected_short_desc.upper()

                        self.logger.debug(f"Checking row {i+1}: {row_text[:100]}...")

                        if expected_upper in row_text:
                            self.logger.info(f"✓ Found matching program in row {i+1}: {expected_short_desc}")
                            row.click()
                            time.sleep(2)
                            return True

                    except Exception as e:
                        self.logger.warning(f"Error checking row {i+1}: {str(e)}")
                        continue

                # If no automatic match found, log available options and ask user
                self.logger.warning(f"✗ No automatic match found for: {expected_short_desc}")
                self.logger.info("Available options:")
                for i, row in enumerate(result_rows):
                    try:
                        self.logger.info(f"  Row {i+1}: {row.text[:100]}...")
                    except:
                        self.logger.info(f"  Row {i+1}: [Unable to read row text]")

                # Interactive selection
                while True:
                    try:
                        choice = input(f"\nSelect row number (1-{len(result_rows)}) or 's' to skip: ").strip().lower()
                        if choice == 's':
                            return False

                        row_num = int(choice) - 1
                        if 0 <= row_num < len(result_rows):
                            result_rows[row_num].click()
                            time.sleep(2)
                            self.logger.info(f"✓ User selected row {choice}")
                            return True
                        else:
                            print(f"Invalid choice. Please enter 1-{len(result_rows)} or 's'")
                    except ValueError:
                        print(f"Invalid input. Please enter 1-{len(result_rows)} or 's'")

        except Exception as e:
            self.logger.error(f"✗ Failed to handle search results: {e}")
            self.logger.debug(f"Search results error details: {traceback.format_exc()}")
            return False

    def update_supervisor(self) -> Tuple[bool, str]:
        """
        Update the supervisor information for the selected student program.

        Returns:
            Tuple[bool, str]: (Success status, Message)
        """
        try:
            self.logger.info("Starting supervisor update process...")

            # Step 1: Click new record button
            if not self._click_new_record_button():
                return False, "Failed to click new record button"

            # Step 2: Fill program action
            if not self._fill_program_action():
                return False, "Failed to fill program action"

            # Step 3: Fill program reason
            if not self._fill_program_reason():
                return False, "Failed to fill program reason"

            # Step 3.5: Click on Postgraduate Details/Supervision tab (MISSING STEP)
            if not self._click_postgraduate_details_tab():
                return False, "Failed to click Postgraduate Details tab"

            # Step 4: Fill supervisor field
            if not self._fill_supervisor_field():
                return False, "Failed to fill supervisor field"

            # Step 5: Save the changes
            if not self._save_changes():
                return False, "Failed to save changes"

            # Step 6: Check for errors
            error_message = self._check_for_errors()
            if error_message:
                return self._handle_save_error(error_message)

            self.logger.info("✓ Supervisor updated successfully")
            return True, "Success"

        except Exception as e:
            self.logger.error(f"✗ Failed to update supervisor: {e}")
            self.logger.debug(f"Update supervisor error details: {traceback.format_exc()}")
            return self._handle_update_exception(e)

    def _click_new_record_button(self) -> bool:
        """Click the new record button"""
        try:
            self.logger.debug("Looking for new record button...")
            new_btn = self.wait.until(EC.element_to_be_clickable((By.ID, "SSR_ACDPRG_AUS$new$0$$0")))
            new_btn.click()
            time.sleep(2)
            self.logger.debug("✓ Clicked new record button")
            return True
        except Exception as e:
            self.logger.error(f"Failed to click new record button: {e}")
            return False

    def _fill_program_action(self) -> bool:
        """Fill the program action field"""
        try:
            prog_action = self.wait.until(EC.element_to_be_clickable((By.ID, "ACAD_PROG_PROG_ACTION$0")))
            prog_action.click()
            time.sleep(1)
            prog_action.clear()
            prog_action.send_keys(self.PROGRAM_ACTION)
            self.logger.debug(f"✓ Entered '{self.PROGRAM_ACTION}' in Program Action")
            return True
        except Exception as e:
            self.logger.error(f"Failed to fill program action: {e}")
            return False

    def _fill_program_reason(self) -> bool:
        """Fill the program reason field"""
        try:
            prog_reason = self.driver.find_element(By.ID, "ACAD_PROG_PROG_REASON$0")
            prog_reason.clear()
            prog_reason.send_keys(self.PROGRAM_REASON)
            self.logger.debug(f"✓ Entered '{self.PROGRAM_REASON}' in Program Reason")
            return True
        except Exception as e:
            self.logger.error(f"Failed to fill program reason: {e}")
            return False

    def _click_postgraduate_details_tab(self) -> bool:
        """
        Click on the Postgraduate Details/Supervision tab.
        This is the missing step between filling program reason and supervisor field.
        """
        try:
            self.logger.debug("Looking for Postgraduate Details/Supervision tab...")

            # Multiple possible selectors for the supervision/postgraduate details tab
            tab_selectors = [
                (By.CSS_SELECTOR, "#ICTAB_8 > span"),  # From archived automation
                (By.ID, "ICTAB_8"),
                (By.XPATH, "//span[contains(text(), 'Supervision')]"),
                (By.XPATH, "//span[contains(text(), 'Postgraduate')]"),
                (By.XPATH, "//span[contains(text(), 'Details')]"),
                (By.CSS_SELECTOR, "[id*='ICTAB'][id*='8']"),
                (By.CSS_SELECTOR, "span[title*='Supervision']"),
                (By.CSS_SELECTOR, "span[title*='Postgraduate']")
            ]

            for selector in tab_selectors:
                try:
                    tab_element = self.wait.until(EC.element_to_be_clickable(selector))
                    tab_element.click()
                    time.sleep(2)  # Wait for tab content to load
                    self.logger.debug(f"✓ Successfully clicked Postgraduate Details tab using selector: {selector}")
                    return True
                except TimeoutException:
                    self.logger.debug(f"Tab selector not found: {selector}")
                    continue
                except Exception as e:
                    self.logger.debug(f"Failed to click tab with selector {selector}: {e}")
                    continue

            # If no selector worked, try to find any tab that might be the supervision tab
            try:
                self.logger.debug("Trying to find supervision tab by text content...")
                tabs = self.driver.find_elements(By.CSS_SELECTOR, "[id*='ICTAB'] span, .PSTABANCHOR span")
                for tab in tabs:
                    try:
                        tab_text = tab.text.lower()
                        if any(keyword in tab_text for keyword in ['supervision', 'postgraduate', 'details', 'research']):
                            tab.click()
                            time.sleep(2)
                            self.logger.debug(f"✓ Found and clicked supervision tab: {tab.text}")
                            return True
                    except Exception:
                        continue
            except Exception as e:
                self.logger.debug(f"Error searching for supervision tab by text: {e}")

            self.logger.warning("Could not find Postgraduate Details/Supervision tab")

            # Ask user for manual intervention
            user_action = input(
                "\nCould not automatically find the Postgraduate Details/Supervision tab.\n"
                "Please manually click on the Postgraduate Details or Supervision tab in the browser,\n"
                "then press Enter to continue, or type 'skip' to skip this step: "
            ).lower().strip()

            if user_action == 'skip':
                self.logger.warning("User chose to skip Postgraduate Details tab click")
                return True  # Continue anyway
            else:
                self.logger.info("User manually clicked Postgraduate Details tab")
                time.sleep(2)
                return True

        except Exception as e:
            self.logger.error(f"Failed to click Postgraduate Details tab: {e}")
            return False

    def _fill_supervisor_field(self) -> bool:
        """Fill the supervisor field with multiple fallback IDs"""
        try:
            supervisor_field_ids = [
                "LU_SUPLU_TBL_LU_SUPERVISOR$0",
                "LU_SUP_LU_TBL_LU_SUPERVISOR$0",
                "LU_SUPERVISOR$0",
                "SUPERVISOR$0"
            ]

            supervisor_field = None
            for field_id in supervisor_field_ids:
                try:
                    supervisor_field = self.wait.until(EC.element_to_be_clickable((By.ID, field_id)))
                    self.logger.debug(f"✓ Found supervisor field with ID: {field_id}")
                    break
                except TimeoutException:
                    self.logger.debug(f"Supervisor field ID not found: {field_id}")
                    continue

            if not supervisor_field:
                self.logger.error("Could not find supervisor field with any known ID")
                return False

            supervisor_field.click()
            time.sleep(1)
            supervisor_field.clear()
            supervisor_field.send_keys(self.supervisor_id)
            self.logger.debug(f"✓ Entered supervisor ID: {self.supervisor_id}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to fill supervisor field: {e}")
            return False

    def _save_changes(self) -> bool:
        """Save the supervisor changes"""
        try:
            save_btn = self.wait.until(EC.element_to_be_clickable((By.ID, "#ICSave")))
            save_btn.click()
            self.logger.debug("✓ Clicked save button")

            # Wait for page to reload
            time.sleep(5)
            return True
        except Exception as e:
            self.logger.error(f"Failed to save changes: {e}")
            return False

    def _check_for_errors(self) -> Optional[str]:
        """Check for error messages after saving"""
        try:
            error_elements = self.driver.find_elements(By.CSS_SELECTOR, ".PSERROR, .PSWARNING, .PSMSGDISPLAY")
            if error_elements:
                error_text = " | ".join([elem.text for elem in error_elements if elem.text.strip()])
                if error_text:
                    self.logger.warning(f"System error detected: {error_text}")
                    return error_text
            return None
        except Exception as e:
            self.logger.debug(f"Error checking for system errors: {e}")
            return None

    def _handle_save_error(self, error_text: str) -> Tuple[bool, str]:
        """Handle errors that occur during save operation"""
        self.logger.error(f"Error after save: {error_text}")

        user_action = input(f"\nError occurred: {error_text}\nChoose: (r)etry, (s)kip, (m)anual fix: ").lower()
        if user_action == 'r':
            return self.update_supervisor()  # Retry
        elif user_action == 'm':
            input("Please fix manually, then press Enter to continue...")
            return True, "Manual fix applied"
        else:
            return False, error_text

    def _handle_update_exception(self, exception: Exception) -> Tuple[bool, str]:
        """Handle exceptions that occur during update process"""
        user_action = input(f"\nException occurred: {exception}\nChoose: (r)etry, (s)kip, (m)anual fix: ").lower()
        if user_action == 'r':
            return self.update_supervisor()  # Retry
        elif user_action == 'm':
            input("Please fix manually, then press Enter to continue...")
            return True, "Manual fix applied"
        else:
            return False, str(exception)

    def process_student(self, student: StudentRecord) -> Tuple[bool, str]:
        """
        Process a single student record.

        Args:
            student: StudentRecord object containing student information

        Returns:
            Tuple[bool, str]: (Success status, Message)
        """
        self.logger.info(f"Processing student: {student.id} - {student.name} - {student.short_desc}")

        try:
            # Navigate to system
            if not self.navigate_to_system():
                return False, "Failed to navigate to system"

            # Search for student
            if not self.search_student(student.id):
                return False, "Failed to search for student"

            # Handle search results
            if not self.handle_search_results(student.short_desc):
                return False, f"No matching program found for {student.short_desc}"

            # Update supervisor
            success, message = self.update_supervisor()

            # Create processing result
            result = ProcessingResult(
                success=success,
                message=message,
                student_id=student.id,
                timestamp=datetime.now(),
                error_details=None if success else message
            )
            self.results['processing_results'].append(result)

            return success, message

        except Exception as e:
            error_msg = f"Error processing student {student.id}: {e}"
            self.logger.error(error_msg)
            self.logger.debug(f"Process student error details: {traceback.format_exc()}")

            # Create error result
            result = ProcessingResult(
                success=False,
                message=str(e),
                student_id=student.id,
                timestamp=datetime.now(),
                error_details=traceback.format_exc()
            )
            self.results['processing_results'].append(result)

            return False, str(e)

    def run_automation(self) -> None:
        """
        Main automation function that orchestrates the entire process.
        """
        self.logger.info("Starting student supervisor automation")

        try:
            # Load student data first
            students = self.load_student_data()
            if not students:
                self.logger.error("No student data loaded")
                return

            # Setup driver
            if not self.setup_driver():
                self.logger.error("Failed to setup WebDriver")
                return

            self.logger.info(f"Processing {len(students)} students...")

            # Process each student
            for i, student in enumerate(students):
                self.results['processed'] += 1

                self.logger.info(f"Processing student {i+1}/{len(students)}: {student.id}")

                success, message = self.process_student(student)

                if success:
                    self.results['successful'] += 1
                    self.logger.info(f"✓ Successfully processed {student.id}")
                else:
                    self.results['failed'] += 1
                    error_msg = f"✗ Failed to process {student.id}: {message}"
                    self.logger.error(error_msg)
                    self.results['errors'].append(error_msg)

                    # Ask user what to do on failure
                    user_choice = input(f"\nFailed to process {student.id} - {student.name}\nError: {message}\nChoose: (c)ontinue, (s)kip remaining, (q)uit: ").lower()

                    if user_choice == 's':
                        self.results['skipped'] = len(students) - i - 1
                        self.logger.info(f"Skipping remaining {len(students) - i - 1} students")
                        break
                    elif user_choice == 'q':
                        self.logger.info("User chose to quit")
                        break

                # Small delay between students to avoid overwhelming the system
                time.sleep(2)

        except KeyboardInterrupt:
            self.logger.warning("Automation interrupted by user")
        except Exception as e:
            self.logger.error(f"Unexpected error in automation: {e}")
            self.logger.debug(f"Automation error details: {traceback.format_exc()}")
        finally:
            # Cleanup
            self.cleanup_driver()

            # Generate and save results
            self._save_results()

            # Print summary
            self.print_summary()

    def _save_results(self) -> None:
        """Save processing results to JSON file for analysis"""
        try:
            results_file = Path("logs") / f"automation_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"

            # Convert results to serializable format
            serializable_results = {
                'summary': {
                    'processed': self.results['processed'],
                    'successful': self.results['successful'],
                    'failed': self.results['failed'],
                    'skipped': self.results['skipped'],
                    'success_rate': (self.results['successful'] / max(self.results['processed'], 1)) * 100
                },
                'errors': self.results['errors'],
                'processing_results': [
                    {
                        'success': result.success,
                        'message': result.message,
                        'student_id': result.student_id,
                        'timestamp': result.timestamp.isoformat(),
                        'error_details': result.error_details
                    }
                    for result in self.results['processing_results']
                ]
            }

            with open(results_file, 'w', encoding='utf-8') as f:
                json.dump(serializable_results, f, indent=2, ensure_ascii=False)

            self.logger.info(f"Results saved to: {results_file}")

        except Exception as e:
            self.logger.warning(f"Failed to save results: {e}")

    def print_summary(self) -> None:
        """Print comprehensive automation summary"""
        self.logger.info("\n" + "="*80)
        self.logger.info("AUTOMATION SUMMARY")
        self.logger.info("="*80)
        self.logger.info(f"Total processed: {self.results['processed']}")
        self.logger.info(f"Successful: {self.results['successful']}")
        self.logger.info(f"Failed: {self.results['failed']}")
        self.logger.info(f"Skipped: {self.results['skipped']}")

        if self.results['processed'] > 0:
            success_rate = (self.results['successful'] / self.results['processed']) * 100
            self.logger.info(f"Success rate: {success_rate:.1f}%")

        if self.results['errors']:
            self.logger.info(f"\nErrors ({len(self.results['errors'])}):")
            for error in self.results['errors']:
                self.logger.info(f"  - {error}")

        self.logger.info("="*80)

        # Print file locations
        self.logger.info(f"Log file: {self.automation_logger.log_file}")
        if self.results['errors']:
            self.logger.info(f"Error log: {self.automation_logger.error_log_file}")


def main():
    """
    Main entry point for the automation system.
    Supports command line arguments for configuration.
    """
    import argparse

    parser = argparse.ArgumentParser(description='Student Supervisor Automation System')
    parser.add_argument('--excel-file', default='student_list.xlsx',
                       help='Path to Excel file containing student data')
    parser.add_argument('--supervisor-id', default='8000401',
                       help='Supervisor ID to assign to students')
    parser.add_argument('--timeout', type=int, default=10,
                       help='Selenium timeout in seconds')
    parser.add_argument('--log-level', choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       default='INFO', help='Logging level')

    args = parser.parse_args()

    try:
        automation = StudentSupervisorAutomation(
            excel_file=args.excel_file,
            supervisor_id=args.supervisor_id,
            timeout=args.timeout,
            log_level=args.log_level
        )
        automation.run_automation()

    except KeyboardInterrupt:
        print("\nAutomation interrupted by user")
    except Exception as e:
        print(f"Failed to start automation: {e}")


if __name__ == "__main__":
    main()
