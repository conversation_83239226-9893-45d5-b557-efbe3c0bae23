"""
Test Chrome logging suppression
This script tests if the Chrome browser logging messages are properly suppressed.
"""

import sys
import time
from student_supervisor_automation import StudentSupervisorAutomation

def test_chrome_logging_suppression():
    """Test if Chrome logging messages are suppressed"""
    print("="*60)
    print("TESTING CHROME LOGGING SUPPRESSION")
    print("="*60)
    print("This test will:")
    print("1. Initialize the automation system")
    print("2. Start Chrome browser")
    print("3. Check if Chrome internal messages are suppressed")
    print("4. Close the browser")
    print()
    
    try:
        # Initialize automation
        print("Initializing automation system...")
        automation = StudentSupervisorAutomation('student_list.xlsx', log_level="INFO")
        
        # Setup driver (this is where Chrome messages would appear)
        print("Starting Chrome browser...")
        print("(If you see Chrome internal messages below, they need to be suppressed)")
        print("-" * 40)
        
        success = automation.setup_driver()
        
        print("-" * 40)
        print("Chrome browser started successfully!" if success else "Failed to start Chrome browser")
        
        if success:
            # Navigate to a simple page to test
            print("Testing navigation...")
            automation.driver.get("https://www.google.com")
            time.sleep(2)
            
            print("✓ Navigation test completed")
            
            # Cleanup
            print("Closing browser...")
            automation.cleanup_driver()
            print("✓ Browser closed successfully")
        
        print("\n" + "="*60)
        print("TEST COMPLETED")
        print("="*60)
        
        if success:
            print("✅ SUCCESS: Chrome browser started and closed without issues")
            print("📝 NOTE: If you saw Chrome internal error messages above,")
            print("         they should now be suppressed in the updated version.")
        else:
            print("❌ FAILED: Could not start Chrome browser")
            
        return success
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        return False

if __name__ == "__main__":
    success = test_chrome_logging_suppression()
    sys.exit(0 if success else 1)
