# Improved Course Advisor Automation Script
import time
import pandas as pd
import logging
import os
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.wait import Web<PERSON>riverWait
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.common.action_chains import ActionChains
from selenium.common.exceptions import (
    TimeoutException,
    ElementClickInterceptedException,
    NoSuchElementException
)

class CourseAdvisorAutomation:
    def __init__(self, excel_file_path=r"c:\Users\<USER>\OneDrive - Lincoln University\Tank\20250704_Selenium_course_advice\Student_List_to_update_Course_Adviser.xlsx"):
        self.excel_file_path = excel_file_path
        self.driver = None
        self.wait = None
        self.vars = {}
        self.setup_logging()
        self.students_data = None
        
    def setup_logging(self):
        """Set up comprehensive logging configuration with logs folder"""
        # Create logs directory if it doesn't exist
        log_dir = "logs"
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)
            print(f"Created logs directory: {log_dir}")

        # Create log filename with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_filename = os.path.join(log_dir, f"course_advisor_automation_{timestamp}.log")

        # Configure detailed logging
        log_formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - [%(funcName)s:%(lineno)d] - %(message)s'
        )

        # File handler for detailed logs
        file_handler = logging.FileHandler(log_filename, encoding='utf-8')
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(log_formatter)

        # Console handler for important messages
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        console_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        console_handler.setFormatter(console_formatter)

        # Configure logger
        self.logger = logging.getLogger('CourseAdvisorAutomation')
        self.logger.setLevel(logging.DEBUG)

        # Clear any existing handlers
        self.logger.handlers.clear()

        self.logger.addHandler(file_handler)
        self.logger.addHandler(console_handler)

        # Prevent duplicate logs
        self.logger.propagate = False

        self.logger.info("=" * 60)
        self.logger.info("Course Advisor Automation Started")
        self.logger.info("=" * 60)
        self.logger.info(f"Log file: {log_filename}")
        self.logger.info(f"Excel file: {self.excel_file_path}")

        # Store log filename for reference
        self.log_filename = log_filename
        
    def setup_driver(self):
        """Initialize the webdriver with proper configuration"""
        try:
            self.driver = webdriver.Firefox()
            self.driver.maximize_window()
            self.wait = WebDriverWait(self.driver, 10)
            self.logger.info("WebDriver initialized successfully")
        except Exception as e:
            self.logger.error(f"Failed to initialize WebDriver: {str(e)}")
            raise
            
    def teardown_driver(self):
        """Clean up the webdriver"""
        if self.driver:
            self.driver.quit()
            self.logger.info("WebDriver closed successfully")

    def safe_click(self, element_locator, timeout=10, scroll_to_element=True):
        """Safely click an element with multiple fallback methods"""
        try:
            # Wait for element to be present
            element = self.wait.until(EC.presence_of_element_located(element_locator))

            # Scroll to element if requested
            if scroll_to_element:
                self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", element)
                time.sleep(0.5)

            # Wait for element to be clickable
            element = self.wait.until(EC.element_to_be_clickable(element_locator))

            # Try normal click first
            try:
                element.click()
                return True
            except ElementClickInterceptedException:
                self.logger.warning(f"Normal click failed for {element_locator}, trying JavaScript click")

                # Try JavaScript click
                try:
                    self.driver.execute_script("arguments[0].click();", element)
                    return True
                except Exception:
                    self.logger.warning(f"JavaScript click failed for {element_locator}, trying ActionChains")

                    # Try ActionChains click
                    try:
                        actions = ActionChains(self.driver)
                        actions.move_to_element(element).click().perform()
                        return True
                    except Exception:
                        self.logger.error(f"All click methods failed for {element_locator}")
                        return False

        except TimeoutException:
            self.logger.error(f"Element not found or not clickable: {element_locator}")
            return False
        except Exception as e:
            self.logger.error(f"Error clicking element {element_locator}: {str(e)}")
            return False

    def safe_send_keys(self, element_locator, text, clear_first=True, send_enter=True, enter_delay=2.0, timeout=10):
        """Safely send keys to an element with multiple fallback methods"""
        try:
            # Wait for element to be present and re-find it to avoid stale reference
            self.logger.debug(f"Waiting for element to be present: {element_locator}")
            element = self.wait.until(EC.presence_of_element_located(element_locator))

            # Scroll to element
            self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", element)
            time.sleep(0.5)

            # Re-find element to ensure it's fresh and clickable
            self.logger.debug(f"Re-finding element to ensure freshness: {element_locator}")
            element = self.wait.until(EC.element_to_be_clickable(element_locator))

            # Try to focus the element using multiple methods
            focused = False

            # Method 1: Normal click
            try:
                element.click()
                focused = True
                self.logger.debug(f"Normal click successful for {element_locator}")
            except ElementClickInterceptedException:
                self.logger.warning(f"Normal click failed for {element_locator}, trying JavaScript click")

                # Method 2: JavaScript click
                try:
                    self.driver.execute_script("arguments[0].click();", element)
                    focused = True
                    self.logger.debug(f"JavaScript click successful for {element_locator}")
                except Exception:
                    self.logger.warning(f"JavaScript click failed for {element_locator}, trying ActionChains")

                    # Method 3: ActionChains
                    try:
                        actions = ActionChains(self.driver)
                        actions.move_to_element(element).click().perform()
                        focused = True
                        self.logger.debug(f"ActionChains click successful for {element_locator}")
                    except Exception:
                        self.logger.warning(f"ActionChains click failed for {element_locator}, trying direct focus")

                        # Method 4: Direct focus with JavaScript
                        try:
                            self.driver.execute_script("arguments[0].focus();", element)
                            focused = True
                            self.logger.debug(f"JavaScript focus successful for {element_locator}")
                        except Exception:
                            self.logger.error(f"All focus methods failed for {element_locator}")

            if not focused:
                self.logger.warning(f"Could not focus element {element_locator}, attempting to send keys anyway")

            # Clear existing text if requested
            if clear_first:
                try:
                    element.clear()
                    self.logger.debug(f"Cleared existing text in {element_locator}")
                except Exception:
                    # Try JavaScript clear if normal clear fails
                    try:
                        self.driver.execute_script("arguments[0].value = '';", element)
                        self.logger.debug(f"JavaScript clear successful for {element_locator}")
                    except Exception as e:
                        self.logger.warning(f"Could not clear element {element_locator}: {str(e)}")

            # Send the text
            try:
                element.send_keys(text)
                self.logger.debug(f"Successfully sent keys '{text}' to {element_locator}")

                # Send Enter key if requested
                if send_enter:
                    self.logger.debug(f"Waiting {enter_delay} seconds before sending Enter key...")
                    time.sleep(enter_delay)  # Wait before sending Enter key

                    # Re-find element before sending Enter to avoid stale reference
                    try:
                        element = self.driver.find_element(*element_locator)
                        element.send_keys(Keys.ENTER)
                        time.sleep(1.0)  # Reduced to 1 second as requested
                        self.logger.debug(f"Sent Enter key to {element_locator}")
                    except Exception as enter_error:
                        self.logger.warning(f"Failed to send Enter key, re-finding element: {str(enter_error)}")
                        element = self.wait.until(EC.element_to_be_clickable(element_locator))
                        element.send_keys(Keys.ENTER)
                        time.sleep(1.0)  # Reduced to 1 second as requested
                        self.logger.debug(f"Sent Enter key after re-finding element")

                return True
            except Exception:
                # Try JavaScript value setting if send_keys fails
                try:
                    self.driver.execute_script("arguments[0].value = arguments[1];", element, text)
                    # Trigger change event
                    self.driver.execute_script("arguments[0].dispatchEvent(new Event('change'));", element)
                    self.logger.debug(f"JavaScript value setting successful for {element_locator}")

                    # Send Enter key if requested
                    if send_enter:
                        try:
                            self.logger.debug(f"Waiting {enter_delay} seconds before sending Enter key...")
                            time.sleep(enter_delay)  # Wait before sending Enter key
                            element.send_keys(Keys.ENTER)
                            time.sleep(0.5)  # Wait for field to process after Enter
                            self.logger.debug(f"Sent Enter key to {element_locator}")
                        except Exception:
                            # Try JavaScript Enter key
                            self.logger.debug(f"Waiting {enter_delay} seconds before JavaScript Enter key...")
                            time.sleep(enter_delay)  # Wait before JavaScript Enter
                            self.driver.execute_script("""
                                var event = new KeyboardEvent('keydown', {key: 'Enter', code: 'Enter'});
                                arguments[0].dispatchEvent(event);
                            """, element)
                            time.sleep(0.5)  # Wait for field to process after Enter
                            self.logger.debug(f"JavaScript Enter key sent to {element_locator}")

                    return True
                except Exception as e:
                    self.logger.error(f"All text input methods failed for {element_locator}: {str(e)}")
                    return False

        except TimeoutException:
            self.logger.error(f"Element not found: {element_locator}")
            return False
        except Exception as e:
            self.logger.error(f"Error sending keys to {element_locator}: {str(e)}")
            return False

    def dismiss_overlays(self):
        """Try to dismiss any overlays or popups that might be blocking elements"""
        try:
            # Try to close any modal dialogs
            modals = [
                "ptModCloseImg_0",
                "ptModalClose",
                "closeButton",
                "btnClose"
            ]

            for modal_id in modals:
                try:
                    modal = self.driver.find_element(By.ID, modal_id)
                    if modal.is_displayed():
                        modal.click()
                        self.logger.debug(f"Closed modal: {modal_id}")
                        time.sleep(0.5)
                except:
                    continue

            # Try to dismiss any alerts
            try:
                alert = self.driver.switch_to.alert
                alert.dismiss()
                self.logger.debug("Dismissed alert")
                time.sleep(0.5)
            except:
                pass

            # Press Escape key to close any dropdowns or overlays
            try:
                from selenium.webdriver.common.keys import Keys
                self.driver.find_element(By.TAG_NAME, "body").send_keys(Keys.ESCAPE)
                self.logger.debug("Sent Escape key to dismiss overlays")
                time.sleep(0.5)
            except:
                pass

        except Exception as e:
            self.logger.debug(f"Error dismissing overlays: {str(e)}")

    def wait_for_page_stability(self, timeout=5):
        """Wait for page to become stable (no loading indicators)"""
        try:
            # Wait for any loading indicators to disappear
            loading_indicators = [
                "WAIT_win0",
                "SAVED_win0",
                "processing",
                "loading"
            ]

            for indicator in loading_indicators:
                try:
                    WebDriverWait(self.driver, timeout).until_not(
                        EC.presence_of_element_located((By.ID, indicator))
                    )
                except:
                    continue

            # Additional wait for JavaScript to complete
            WebDriverWait(self.driver, timeout).until(
                lambda driver: driver.execute_script("return document.readyState") == "complete"
            )

            self.logger.debug("Page stability confirmed")
            return True

        except Exception as e:
            self.logger.debug(f"Page stability check failed: {str(e)}")
            return False
            
    def load_student_data(self):
        """Load student data from Excel file"""
        try:
            self.students_data = pd.read_excel(self.excel_file_path)
            self.logger.info(f"Loaded {len(self.students_data)} students from Excel file")
            self.logger.info(f"Columns: {self.students_data.columns.tolist()}")

            # Display the data structure for verification
            print(f"\nLoaded {len(self.students_data)} students:")
            print("Columns:", self.students_data.columns.tolist())
            print("\nFirst few rows:")
            print(self.students_data.head())

            return True
        except FileNotFoundError:
            self.logger.error(f"Excel file not found: {self.excel_file_path}")
            # Try CSV as fallback
            csv_path = self.excel_file_path.replace('.xlsx', '.csv')
            try:
                self.students_data = pd.read_csv(csv_path)
                self.logger.info(f"Loaded {len(self.students_data)} students from CSV file")
                print(f"\nLoaded {len(self.students_data)} students from CSV:")
                print("Columns:", self.students_data.columns.tolist())
                return True
            except:
                self.logger.error(f"CSV file also not found: {csv_path}")
                return False
        except PermissionError:
            self.logger.error(f"Permission denied accessing Excel file: {self.excel_file_path}")
            print(f"Please close the Excel file '{self.excel_file_path}' and try again.")
            return False
        except Exception as e:
            self.logger.error(f"Error loading Excel file: {str(e)}")
            return False
            
    def wait_for_user_confirmation(self, message):
        """Wait for user confirmation before proceeding"""
        response = input(f"\n{message}\nPress 'y' to continue, 'n' to stop, or 's' to skip this student: ").lower().strip()
        if response == 'n':
            self.logger.info("User chose to stop the automation")
            return 'stop'
        elif response == 's':
            self.logger.info("User chose to skip this student")
            return 'skip'
        elif response == 'y':
            return 'continue'
        else:
            print("Invalid input. Please enter 'y', 'n', or 's'")
            return self.wait_for_user_confirmation(message)
            
    def login_to_system(self, username="<EMAIL>", password="aDlszywhn8@ls"):
        """Login to the system"""
        try:
            self.logger.info("Starting login process")
            self.driver.get("https://lucas.lincoln.ac.nz/logon/LogonPoint/tmindex.html")
            
            # Enter password
            password_field = self.wait.until(EC.element_to_be_clickable((By.ID, "passwd")))
            password_field.click()
            password_field.send_keys(password)
            
            # Enter username
            username_field = self.driver.find_element(By.ID, "login")
            username_field.click()
            username_field.send_keys(username)
            
            # Click login button
            login_button = self.driver.find_element(By.ID, "nsg-x1-logon-button")
            login_button.click()
            
            # Navigate to Records and Enrolment
            records_link = self.wait.until(EC.element_to_be_clickable((By.ID, "LU_RECORDS_AND_ENROLMENT$3")))
            records_link.click()
            
            # Switch to frame
            self.wait.until(EC.frame_to_be_available_and_switch_to_it(0))
            
            self.logger.info("Login successful")
            return True
            
        except TimeoutException:
            self.logger.error("Timeout during login process")
            return False
        except Exception as e:
            self.logger.error(f"Error during login: {str(e)}")
            return False
            
    def search_student(self, student_id):
        """Search for a student by ID"""
        try:
            self.logger.info(f"Searching for student ID: {student_id}")
            
            # Click on student ID field and enter the ID
            student_id_field = self.wait.until(EC.element_to_be_clickable((By.ID, "STDNT_CAR_SRCH_EMPLID")))
            student_id_field.clear()
            student_id_field.click()
            student_id_field.send_keys(str(student_id))
            
            # Click search button
            search_button = self.driver.find_element(By.ID, "PTS_CFG_CL_WRK_PTS_SRCH_BTN")
            search_button.click()
            
            # Wait for results to load
            time.sleep(2)
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error searching for student {student_id}: {str(e)}")
            return False
            
    def handle_search_results(self, expected_program=None):
        """Handle search results - select correct record if multiple found"""
        try:
            # Check if we have multiple results by looking for result rows
            result_rows = self.driver.find_elements(By.CSS_SELECTOR, "[id*='PTS_CFG_CL_RSLT_NUI_SRCH3']")
            
            if len(result_rows) == 0:
                self.logger.warning("No search results found")
                return False
                
            elif len(result_rows) == 1:
                self.logger.info("Single result found, clicking it")
                result_rows[0].click()
                return True
                
            else:
                self.logger.info(f"Multiple results found ({len(result_rows)}), need to select correct one")
                
                if expected_program:
                    # Try to find the row with matching program description
                    for i, row in enumerate(result_rows):
                        try:
                            # Get the text content of the row to check for program match
                            row_text = row.text.upper()
                            if expected_program.upper() in row_text:
                                self.logger.info(f"Found matching program in row {i+1}: {expected_program}")
                                row.click()
                                return True
                        except Exception as e:
                            self.logger.warning(f"Error checking row {i+1}: {str(e)}")
                            continue
                
                # If no automatic match found, ask user to select
                print(f"\nFound {len(result_rows)} results for this student:")
                for i, row in enumerate(result_rows):
                    try:
                        print(f"{i+1}. {row.text}")
                    except:
                        print(f"{i+1}. [Unable to read row text]")
                
                while True:
                    try:
                        choice = input(f"Please select the correct record (1-{len(result_rows)}) or 's' to skip: ").strip()
                        if choice.lower() == 's':
                            return False
                        choice_num = int(choice)
                        if 1 <= choice_num <= len(result_rows):
                            result_rows[choice_num - 1].click()
                            self.logger.info(f"User selected row {choice_num}")
                            return True
                        else:
                            print(f"Please enter a number between 1 and {len(result_rows)}")
                    except ValueError:
                        print("Please enter a valid number or 's' to skip")
                        
        except Exception as e:
            self.logger.error(f"Error handling search results: {str(e)}")
            return False

    def update_course_advisor_complete(self, faculty="FESD", department="SOLA", staff_id="1074800", role="Supervisor", study_start_date=None):
        """Update the course advisor information following exact Selenium IDE steps"""
        try:
            self.logger.info(f"Updating course advisor - Faculty: {faculty}, Department: {department}, Staff ID: {staff_id}, Role: {role}")

            # Wait for page to load and ensure stability
            time.sleep(3)
            self.wait_for_page_stability()
            self.dismiss_overlays()
            self.driver.execute_script("window.scrollTo(0, 0);")
            time.sleep(1)

            # Step 17: Click on academic program section
            self.logger.info("Step 17: Clicking academic program section")
            if not self.safe_click((By.ID, "SSR_ACDPRG_AUS$new$0$$0")):
                self.logger.error("Failed to click academic program section")
                return False

            # Wait for page to load/refresh after clicking academic program section
            self.logger.debug("Waiting for page to stabilize after academic program section click...")
            time.sleep(3)  # Optimized wait time for page refresh
            self.wait_for_page_stability()  # Wait for any dynamic content to load

            # Step 18: Enter "DATA" in program action field and press Enter
            self.logger.info("Step 18: Entering 'DATA' in program action field")
            self.dismiss_overlays()  # Clear any overlays before interaction
            if not self.safe_send_keys((By.ID, "ACAD_PROG_PROG_ACTION$0"), "DATA", send_enter=True, enter_delay=2.0):
                self.logger.error("Failed to enter 'DATA' in program action field")
                return False
            time.sleep(2)  # Wait for field to process

            # Step 19: Enter "PGRD" in action reason field and press Enter
            self.logger.info("Step 19: Entering 'PGRD' in action reason field")
            self.dismiss_overlays()  # Clear any overlays before interaction
            if not self.safe_send_keys((By.ID, "ACAD_PROG_PROG_REASON$0"), "PGRD", send_enter=True, enter_delay=2.0):
                self.logger.error("Failed to enter 'PGRD' in action reason field")
                return False
            time.sleep(1)  # Reduced wait time for field processing

            # Step 20: Click on options list (optional - may not always be present)
            self.logger.info("Step 20: Clicking options list")
            try:
                if self.safe_click((By.ID, "OptionsList_0"), timeout=5):
                    self.logger.debug("Options list clicked successfully")
                    time.sleep(1)  # Reduced wait time
                else:
                    self.logger.debug("Options list not found or not clickable, continuing...")
            except Exception as e:
                self.logger.debug(f"Options list interaction failed: {str(e)}, continuing...")

            # Step 21: Click on the supervision tab
            self.logger.info("Step 21: Clicking supervision tab")
            if not self.safe_click((By.CSS_SELECTOR, "#ICTAB_8 > span")):
                self.logger.error("Failed to click supervision tab")
                return False
            time.sleep(2)

            # Step 22-24: Enter academic organization (Faculty) and press Enter
            self.logger.info(f"Step 22-24: Entering academic organization: {faculty}")
            if not self.safe_send_keys((By.ID, "LU_STDT_RSH_TBL_ACAD_ORG$0"), faculty, send_enter=True, enter_delay=2.0):
                self.logger.error("Failed to enter academic organization")
                return False
            time.sleep(3)  # Wait longer for field to process and lookup

            # Step 25: Click on the dropdown option
            self.logger.info("Step 25: Clicking dropdown option")
            if not self.safe_click((By.CSS_SELECTOR, "#ACE_\\$ICField18\\$0 tr:nth-child(5) > td:nth-child(2)")):
                self.logger.warning("Failed to click dropdown option, continuing...")
            time.sleep(1)

            # Step 26-27: Enter academic group (Department) and press Enter
            self.logger.info(f"Step 26-27: Entering academic group: {department}")
            if not self.safe_send_keys((By.ID, "LU_STDT_RSH_TBL_ACAD_GROUP$0"), department, send_enter=True, enter_delay=2.0):
                self.logger.error("Failed to enter academic group")
                return False
            time.sleep(2)  # Wait for field to process and lookup

            # Step 28-29: Switch to parent frame and close modal (optional)
            self.logger.info("Step 28-29: Switching frames and closing modal")
            try:
                self.driver.switch_to.default_content()
                # Try to close modal if it exists
                try:
                    if self.safe_click((By.ID, "ptModCloseImg_0"), timeout=3):
                        self.logger.debug("Modal closed successfully")
                        time.sleep(1)
                    else:
                        self.logger.debug("Modal close button not found, continuing...")
                except Exception as modal_error:
                    self.logger.debug(f"Modal handling failed: {str(modal_error)}, continuing...")
            except Exception as e:
                self.logger.warning(f"Modal close failed: {str(e)}, continuing...")

            # Step 30: Switch back to main frame
            self.logger.info("Step 30: Switching back to main frame")
            self.driver.switch_to.frame(0)
            time.sleep(1)

            # Step 31-32: Click on start date prompt and select date
            self.logger.info("Step 31-32: Setting start date")
            if not self.safe_click((By.ID, "LU_STDT_RSH_TBL_START_DT$prompt$img$0")):
                self.logger.warning("Failed to click start date prompt, continuing...")
            else:
                time.sleep(1)
                # Select date (use study start date if provided, otherwise use default)
                if study_start_date:
                    # TODO: Implement date selection based on study_start_date
                    # For now, use the default date selection
                    pass

                if not self.safe_click((By.CSS_SELECTOR, "tbody:nth-child(2) > tr:nth-child(3) > td:nth-child(3)")):
                    self.logger.warning("Failed to select date, continuing...")
                time.sleep(1)

            # Step 33-35: Enter supervisor ID and press Enter
            self.logger.info(f"Step 33-35: Entering supervisor ID: {staff_id}")
            if not self.safe_send_keys((By.ID, "LU_SUP_LU_TBL_LU_SUPERVISOR$0"), staff_id, send_enter=True, enter_delay=2.0):
                self.logger.error("Failed to enter supervisor ID")
                return False
            time.sleep(3)  # Wait longer for field to process and lookup supervisor

            # Step 36: Click on supervisor role field
            self.logger.info("Step 36: Clicking supervisor role field")
            if not self.safe_click((By.CSS_SELECTOR, "#trLU_SUP_LU_TBL\\$0_row1 > .PSLEVEL3GRIDODDROW:nth-child(4)")):
                self.logger.warning("Failed to click supervisor role field, trying alternative...")
            time.sleep(1)

            # Step 37-39: Select supervisor role
            self.logger.info(f"Step 37-39: Selecting supervisor role: {role}")
            if not self.safe_click((By.ID, "LU_SUP_LU_TBL_LU_ROLE$0")):
                self.logger.error("Failed to click role dropdown")
                return False

            time.sleep(1)
            try:
                role_dropdown = self.driver.find_element(By.ID, "LU_SUP_LU_TBL_LU_ROLE$0")
                supervisor_option = role_dropdown.find_element(By.XPATH, f"//option[. = '{role}']")
                supervisor_option.click()
                time.sleep(1)
                self.logger.debug(f"Selected supervisor role: {role}")

                # Additional click as per Selenium IDE
                try:
                    additional_option = self.driver.find_element(By.CSS_SELECTOR, "option:nth-child(6)")
                    additional_option.click()
                    self.logger.debug("Additional option click completed")
                except Exception:
                    self.logger.warning("Additional option click failed, continuing...")

            except Exception as e:
                self.logger.error(f"Failed to select supervisor role: {str(e)}")
                return False

            # Step 40: Save the changes
            self.logger.info("Step 40: Saving changes")
            if not self.safe_click((By.ID, "#ICSave")):
                # Try alternative save button selector
                if not self.safe_click((By.ID, "ICSave")):
                    self.logger.error("Failed to click save button")
                    return False

            self.logger.info("Course advisor update completed successfully!")
            self.logger.info("Waiting for save verification...")
            time.sleep(5)  # Wait longer for save to process

            return True

        except Exception as e:
            self.logger.error(f"Error updating course advisor: {str(e)}")
            return False

    def verify_save_success(self):
        """Verify if the save operation was successful"""
        try:
            # Wait a moment for the page to respond
            time.sleep(3)

            # Check if page reloaded/redirected (indicates success)
            current_url = self.driver.current_url
            self.logger.info(f"Current URL after save: {current_url}")

            # Check for error messages on the page
            error_elements = self.driver.find_elements(By.CSS_SELECTOR, ".PSERRORTEXT, .PSERRORTEXTFATAL, .error, [id*='error'], [class*='error']")

            if error_elements:
                # Found error messages
                self.logger.warning("Error messages found on page after save")
                return 'error'

            # Check if we're still in the same form or if page has changed
            try:
                # Try to find the save button - if it's still there, we might still be on the same page
                save_button = self.driver.find_element(By.ID, "#ICSave")
                if save_button.is_displayed():
                    # Still on the same page, check for success indicators
                    success_elements = self.driver.find_elements(By.CSS_SELECTOR, ".PSSUCCESSTEXT, .success, [id*='success'], [class*='success']")
                    if success_elements:
                        return 'success'
                    else:
                        # No clear success or error message
                        return 'unknown'
                else:
                    # Save button not visible, likely redirected
                    return 'success'
            except:
                # Save button not found, likely redirected to a different page
                self.logger.info("Save button not found, assuming successful redirect")
                return 'success'

        except Exception as e:
            self.logger.error(f"Error verifying save success: {str(e)}")
            return 'unknown'

    def get_error_message(self):
        """Get error message from the page"""
        try:
            error_elements = self.driver.find_elements(By.CSS_SELECTOR, ".PSERRORTEXT, .PSERRORTEXTFATAL, .error, [id*='error'], [class*='error']")

            if error_elements:
                error_messages = []
                for element in error_elements:
                    if element.is_displayed() and element.text.strip():
                        error_messages.append(element.text.strip())

                if error_messages:
                    return "; ".join(error_messages)

            return "Unknown error - no specific error message found"

        except Exception as e:
            self.logger.error(f"Error getting error message: {str(e)}")
            return f"Error retrieving error message: {str(e)}"

    def process_single_student(self, student_row):
        """Process a single student record"""
        try:
            # Extract student information from the row using actual Excel column names
            student_id = student_row.get('Student ID', student_row.get('Student_ID', student_row.get('StudentID')))
            program = student_row.get('Program Short Descr', student_row.get('Program', ''))
            faculty = student_row.get('Faculty', 'FESD')
            department = student_row.get('Department', 'SOLA')
            staff_id = student_row.get('Staff ID', student_row.get('Staff IDD', student_row.get('Supervisor_ID', '1074800')))
            role = student_row.get('Role', 'Supervisor')

            study_start_date = student_row.get('Study Start Date', '')

            if pd.isna(student_id):
                self.logger.warning("Student ID is missing, skipping this record")
                return False

            self.logger.info(f"Processing student: {student_id}, Program: {program}, Faculty: {faculty}, Department: {department}")

            # Ask for user confirmation before processing
            confirmation = self.wait_for_user_confirmation(
                f"About to process student {student_id} (Program: {program}, Faculty: {faculty}, Department: {department}). Continue?"
            )

            if confirmation == 'stop':
                return 'stop'
            elif confirmation == 'skip':
                return 'skip'

            # Search for the student
            if not self.search_student(student_id):
                self.logger.error(f"Failed to search for student {student_id}")
                return False

            # Handle search results
            if not self.handle_search_results(program):
                self.logger.error(f"Failed to select correct record for student {student_id}")
                return False

            # Update course advisor with all the data from Excel
            if not self.update_course_advisor_complete(
                faculty=faculty,
                department=department,
                staff_id=str(staff_id),
                role=role,
                study_start_date=study_start_date
            ):
                self.logger.error(f"Failed to update course advisor for student {student_id}")
                return False

            # Verify save was successful
            save_result = self.verify_save_success()
            if save_result == 'error':
                error_msg = self.get_error_message()
                self.logger.error(f"Save failed for student {student_id}: {error_msg}")

                intervention = self.wait_for_user_confirmation(
                    f"Save failed for student {student_id} with error: {error_msg}\n"
                    f"Please resolve this manually in the browser, then continue."
                )

                if intervention == 'stop':
                    return 'stop'
                elif intervention == 'skip':
                    return 'skip'
            elif save_result == 'success':
                self.logger.info(f"Successfully saved course advisor update for student {student_id}")

            return True

        except Exception as e:
            self.logger.error(f"Error processing student: {str(e)}")
            return False

    def run_automation(self):
        """Main method to run the complete automation"""
        try:
            self.logger.info("Starting Course Advisor Automation")

            # Load student data
            if not self.load_student_data():
                self.logger.error("Failed to load student data")
                return False

            # Setup webdriver
            self.setup_driver()

            # Login to system
            if not self.login_to_system():
                self.logger.error("Failed to login to system")
                return False

            # Process each student
            processed_count = 0
            skipped_count = 0
            failed_count = 0

            for index, student_row in self.students_data.iterrows():
                self.logger.info(f"Processing student {index + 1} of {len(self.students_data)}")

                result = self.process_single_student(student_row)

                if result == 'stop':
                    self.logger.info("Automation stopped by user")
                    break
                elif result == 'skip':
                    skipped_count += 1
                    self.logger.info(f"Skipped student at row {index + 1}")
                elif result:
                    processed_count += 1
                    self.logger.info(f"Successfully processed student at row {index + 1}")
                else:
                    failed_count += 1
                    self.logger.error(f"Failed to process student at row {index + 1}")

                    # Ask if user wants to continue after failure
                    continue_after_failure = self.wait_for_user_confirmation(
                        f"Failed to process student at row {index + 1}. Continue with next student?"
                    )

                    if continue_after_failure == 'stop':
                        break

            # Summary
            self.logger.info(f"Automation completed. Processed: {processed_count}, Skipped: {skipped_count}, Failed: {failed_count}")
            print(f"\nAutomation Summary:")
            print(f"Successfully processed: {processed_count}")
            print(f"Skipped: {skipped_count}")
            print(f"Failed: {failed_count}")

            return True

        except Exception as e:
            self.logger.error(f"Error in main automation: {str(e)}")
            return False
        finally:
            self.teardown_driver()


# Main execution
if __name__ == "__main__":
    automation = CourseAdvisorAutomation()
    automation.run_automation()
