import time
import logging
import openpyxl
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.wait import WebDriverWait
from selenium.webdriver.chrome.options import Options

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_proven_approach():
    """Test using the proven approach from archived automation"""
    
    # Load first student
    try:
        wb = openpyxl.load_workbook('student_list.xlsx')
        ws = wb.active
        first_row = list(ws.iter_rows(min_row=2, max_row=2, values_only=True))[0]
        student = {
            'id': str(first_row[0]),
            'name': first_row[1],
            'short_desc': first_row[6]
        }
        logger.info(f"Testing with: {student['id']} - {student['short_desc']}")
    except Exception as e:
        logger.error(f"Failed to load student data: {e}")
        return
    
    # Setup Chrome driver
    chrome_options = Options()
    chrome_options.add_argument("--start-maximized")
    driver = webdriver.Chrome(options=chrome_options)
    wait = WebDriverWait(driver, 10)
    
    try:
        # Navigate and search
        url = "https://lucas.lincoln.ac.nz/psc/ps/EMPLOYEE/SA/c/NUI_FRAMEWORK.PT_AGSTARTPAGE_NUI.GBL?CONTEXTIDPARAMS=TEMPLATE_ID%3aPTPPNAVCOL&scname=ADMN_RECORDS_AND_ENROLMENT&PanelCollapsible=Y&PTPPB_GROUPLET_ID=LU_RECORDS_AND_ENROLMENT&CRefName=LU_RECORDS_AND_ENROLMENT_TILE"
        driver.get(url)
        
        input("Please login and press Enter to continue...")
        
        # Switch to frame and search
        try:
            driver.switch_to.frame(0)
        except:
            pass
        
        search_field = wait.until(EC.element_to_be_clickable((By.ID, "STDNT_CAR_SRCH_EMPLID")))
        search_field.clear()
        search_field.send_keys(student['id'])
        
        search_btn = wait.until(EC.element_to_be_clickable((By.ID, "PTS_CFG_CL_WRK_PTS_SRCH_BTN")))
        search_btn.click()
        
        time.sleep(3)
        
        # Use the proven approach from archived automation
        logger.info("Using proven CSS selector approach")
        
        # Find all result rows using the selector from the working automation
        result_rows = driver.find_elements(By.CSS_SELECTOR, "[id*='PTS_CFG_CL_RSLT_NUI_SRCH3']")
        logger.info(f"Found {len(result_rows)} result rows")
        
        if len(result_rows) == 0:
            logger.warning("No search results found")
            return False
            
        elif len(result_rows) == 1:
            logger.info("Single result found, clicking it")
            result_rows[0].click()
            logger.info("✓ Successfully clicked single result")
            time.sleep(3)
            return True
            
        else:
            logger.info(f"Multiple results found ({len(result_rows)}), searching for matching program")
            
            # Show all available options
            print(f"\nFound {len(result_rows)} results:")
            for i, row in enumerate(result_rows):
                try:
                    print(f"{i+1}. {row.text}")
                except:
                    print(f"{i+1}. [Unable to read row text]")
            
            # Try to find the row with matching program description
            for i, row in enumerate(result_rows):
                try:
                    # Get the text content of the row to check for program match
                    row_text = row.text.upper()
                    expected_upper = student['short_desc'].upper()
                    
                    logger.info(f"Checking row {i+1}: {row_text[:100]}...")
                    
                    if expected_upper in row_text:
                        logger.info(f"✓ Found matching program in row {i+1}: {student['short_desc']}")
                        row.click()
                        logger.info("✓ Successfully clicked matching row")
                        time.sleep(3)
                        return True
                        
                except Exception as e:
                    logger.warning(f"Error checking row {i+1}: {str(e)}")
                    continue
            
            # If no automatic match found, ask user to select
            logger.warning(f"No automatic match found for: {student['short_desc']}")
            
            while True:
                try:
                    choice = input(f"Please select the correct record (1-{len(result_rows)}) or 's' to skip: ").strip()
                    if choice.lower() == 's':
                        logger.info("User chose to skip")
                        return False
                    choice_num = int(choice)
                    if 1 <= choice_num <= len(result_rows):
                        result_rows[choice_num - 1].click()
                        logger.info(f"✓ User selected row {choice_num}")
                        time.sleep(3)
                        return True
                    else:
                        print(f"Please enter a number between 1 and {len(result_rows)}")
                except ValueError:
                    print("Please enter a valid number or 's' to skip")
        
    except Exception as e:
        logger.error(f"Error during test: {e}")
        return False
        
    finally:
        input("Press Enter to close browser...")
        driver.quit()

if __name__ == "__main__":
    success = test_proven_approach()
    if success:
        print("\n✓ Proven approach worked! Row selection successful.")
    else:
        print("\n✗ Row selection failed or was skipped.")
