import time
import logging
import openpyxl
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.wait import WebDriverWait
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.action_chains import ActionChains

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def simple_automated_click():
    """Simple approach to automatically click the correct row"""
    
    # Load first student
    try:
        wb = openpyxl.load_workbook('student_list.xlsx')
        ws = wb.active
        first_row = list(ws.iter_rows(min_row=2, max_row=2, values_only=True))[0]
        student = {
            'id': str(first_row[0]),
            'name': first_row[1],
            'short_desc': first_row[6]
        }
        logger.info(f"Testing with: {student['id']} - {student['short_desc']}")
    except Exception as e:
        logger.error(f"Failed to load student data: {e}")
        return
    
    # Setup Chrome driver
    chrome_options = Options()
    chrome_options.add_argument("--start-maximized")
    driver = webdriver.Chrome(options=chrome_options)
    wait = WebDriverWait(driver, 10)
    
    try:
        # Navigate and search
        url = "https://lucas.lincoln.ac.nz/psc/ps/EMPLOYEE/SA/c/NUI_FRAMEWORK.PT_AGSTARTPAGE_NUI.GBL?CONTEXTIDPARAMS=TEMPLATE_ID%3aPTPPNAVCOL&scname=ADMN_RECORDS_AND_ENROLMENT&PanelCollapsible=Y&PTPPB_GROUPLET_ID=LU_RECORDS_AND_ENROLMENT&CRefName=LU_RECORDS_AND_ENROLMENT_TILE"
        driver.get(url)
        
        input("Please login and press Enter to continue...")
        
        # Switch to frame and search
        try:
            driver.switch_to.frame(0)
        except:
            pass
        
        search_field = wait.until(EC.element_to_be_clickable((By.ID, "STDNT_CAR_SRCH_EMPLID")))
        search_field.clear()
        search_field.send_keys(student['id'])
        
        search_btn = wait.until(EC.element_to_be_clickable((By.ID, "PTS_CFG_CL_WRK_PTS_SRCH_BTN")))
        search_btn.click()
        
        time.sleep(3)
        
        # Simple approach: Find all elements containing the target text
        logger.info(f"Looking for elements containing: {student['short_desc']}")
        
        # Method 1: Find all elements containing the text
        all_elements = driver.find_elements(By.XPATH, f"//*[contains(text(), '{student['short_desc']}')]")
        logger.info(f"Found {len(all_elements)} elements containing '{student['short_desc']}'")
        
        for i, element in enumerate(all_elements):
            print(f"Element {i}: {element.tag_name} - {element.text}")
            print(f"  ID: {element.get_attribute('id')}")
            print(f"  Class: {element.get_attribute('class')}")
            
            # Try to find the parent row
            try:
                parent_row = element.find_element(By.XPATH, "./ancestor::tr[1]")
                print(f"  Parent row found: {parent_row.get_attribute('id')}")
                print(f"  Parent row text: {parent_row.text[:100]}...")
                
                # Try to click the parent row
                try:
                    print(f"  Attempting to click parent row...")
                    parent_row.click()
                    logger.info("✓ Successfully clicked parent row!")
                    time.sleep(3)
                    
                    # Check if we moved to a new page
                    new_url = driver.current_url
                    print(f"  New URL: {new_url}")
                    
                    if new_url != url:
                        logger.info("✓ Page changed - click was successful!")
                        break
                    else:
                        logger.info("Page didn't change, trying next element...")
                        
                except Exception as e:
                    print(f"  ✗ Failed to click parent row: {e}")
                    
                    # Try JavaScript click
                    try:
                        print(f"  Trying JavaScript click...")
                        driver.execute_script("arguments[0].click();", parent_row)
                        logger.info("✓ JavaScript click successful!")
                        time.sleep(3)
                        break
                    except Exception as e2:
                        print(f"  ✗ JavaScript click failed: {e2}")
                        
            except Exception as e:
                print(f"  No parent row found: {e}")
        
        # Method 2: If Method 1 didn't work, try clicking the text element directly
        if len(all_elements) > 0:
            logger.info("Trying to click text elements directly...")
            for i, element in enumerate(all_elements):
                try:
                    print(f"Clicking element {i} directly...")
                    element.click()
                    logger.info(f"✓ Clicked element {i} successfully!")
                    time.sleep(3)
                    
                    new_url = driver.current_url
                    if new_url != url:
                        logger.info("✓ Page changed - click was successful!")
                        break
                        
                except Exception as e:
                    print(f"✗ Failed to click element {i}: {e}")
        
        # Method 3: Use ActionChains for more precise clicking
        logger.info("Trying ActionChains approach...")
        for i, element in enumerate(all_elements):
            try:
                print(f"Using ActionChains on element {i}...")
                actions = ActionChains(driver)
                actions.move_to_element(element).click().perform()
                logger.info(f"✓ ActionChains click on element {i} successful!")
                time.sleep(3)
                
                new_url = driver.current_url
                if new_url != url:
                    logger.info("✓ Page changed - click was successful!")
                    break
                    
            except Exception as e:
                print(f"✗ ActionChains failed on element {i}: {e}")
        
        print("\nCurrent page state:")
        print(f"URL: {driver.current_url}")
        print(f"Title: {driver.title}")
        
    except Exception as e:
        logger.error(f"Error during test: {e}")
        
    finally:
        input("Press Enter to close browser...")
        driver.quit()

if __name__ == "__main__":
    simple_automated_click()
