2025-08-05 13:48:02,390 - INFO - Starting student supervisor automation
2025-08-05 13:48:04,249 - INFO - Chrome driver initialized successfully
2025-08-05 13:48:04,277 - INFO - Excel headers: ['ID', 'Name', 'Career', 'Eff Date', 'Status', 'Acad Prog', 'Short Desc']
2025-08-05 13:48:04,279 - INFO - Loaded 147 students from Excel file
2025-08-05 13:48:04,280 - INFO - Processing student 1/147: 1042457
2025-08-05 13:48:04,280 - INFO - Processing student: 1042457 - <PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON> - PCEnvMgmt
2025-08-05 13:48:05,808 - INFO - Navigated to student system
2025-08-05 13:48:19,106 - ERROR - Failed to search for student 1042457: Message: 
Stacktrace:
	GetHandleVerifier [0x0x7ff60be9e415+77285]
	GetHandleVerifier [0x0x7ff60be9e470+77376]
	(No symbol) [0x0x7ff60bc69a6a]
	(No symbol) [0x0x7ff60bcc0406]
	(No symbol) [0x0x7ff60bcc06bc]
	(No symbol) [0x0x7ff60bd13ac7]
	(No symbol) [0x0x7ff60bce864f]
	(No symbol) [0x0x7ff60bd1087f]
	(No symbol) [0x0x7ff60bce83e3]
	(No symbol) [0x0x7ff60bcb1521]
	(No symbol) [0x0x7ff60bcb22b3]
	GetHandleVerifier [0x0x7ff60c181efd+3107021]
	GetHandleVerifier [0x0x7ff60c17c29d+3083373]
	GetHandleVerifier [0x0x7ff60c19bedd+3213485]
	GetHandleVerifier [0x0x7ff60beb884e+184862]
	GetHandleVerifier [0x0x7ff60bec055f+216879]
	GetHandleVerifier [0x0x7ff60bea7084+113236]
	GetHandleVerifier [0x0x7ff60bea7239+113673]
	GetHandleVerifier [0x0x7ff60be8e298+11368]
	BaseThreadInitThunk [0x0x7ffff095259d+29]
	RtlUserThreadStart [0x0x7ffff210af78+40]

2025-08-05 13:48:30,005 - INFO - Processing student 2/147: 1063788
2025-08-05 13:48:30,006 - INFO - Processing student: 1063788 - Parker,Mark Danial - PCEnvMgmt
2025-08-05 13:48:30,506 - INFO - Navigated to student system
2025-08-05 13:48:33,619 - INFO - Entered student ID: 1063788
2025-08-05 13:48:33,773 - INFO - Clicked search button
2025-08-05 13:48:36,774 - INFO - Using proven CSS selector approach for search results
2025-08-05 13:48:36,795 - INFO - Found 12 result rows
2025-08-05 13:48:36,795 - INFO - Multiple results found (12), searching for matching program
2025-08-05 13:48:36,818 - INFO - Checking row 1: CONT ED...
2025-08-05 13:48:36,835 - INFO - Checking row 2: CONT ED...
2025-08-05 13:48:36,852 - INFO - Checking row 3: PCENVMGMT...
2025-08-05 13:48:39,047 - INFO - Starting supervisor update process...
2025-08-05 13:48:39,047 - INFO - Looking for new record button...
2025-08-05 13:49:23,883 - ERROR - Could not find supervisor field with any known ID
2025-08-05 13:49:25,890 - INFO - Processing student 3/147: 1094839
2025-08-05 13:49:25,891 - INFO - Processing student: 1094839 - Chesterton,Samantha Karen - PCEnvMgmt
2025-08-05 13:49:26,454 - INFO - Navigated to student system
2025-08-05 13:49:29,621 - INFO - Entered student ID: 1094839
2025-08-05 13:49:29,732 - INFO - Clicked search button
2025-08-05 13:49:32,733 - INFO - Using proven CSS selector approach for search results
2025-08-05 13:49:32,746 - INFO - Found 6 result rows
2025-08-05 13:49:32,746 - INFO - Multiple results found (6), searching for matching program
2025-08-05 13:49:32,762 - INFO - Checking row 1: PCENVMGMT...
2025-08-05 13:49:34,975 - INFO - Starting supervisor update process...
2025-08-05 13:49:34,976 - INFO - Looking for new record button...
2025-08-05 13:50:14,863 - ERROR - Could not find supervisor field with any known ID
2025-08-05 13:50:16,868 - INFO - Processing student 4/147: 1100672
2025-08-05 13:50:16,869 - INFO - Processing student: 1100672 - Manawatu,Rachel Jane - PCEnvMgmt
2025-08-05 13:50:20,930 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000266F42D41A0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9f5d31ea683feeecd6dac9d04c694c0c/url
2025-08-05 13:50:25,012 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000266F423B890>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9f5d31ea683feeecd6dac9d04c694c0c/url
2025-08-05 13:50:29,049 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000266F430C410>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9f5d31ea683feeecd6dac9d04c694c0c/url
2025-08-05 13:50:33,107 - ERROR - Failed to navigate to system: HTTPConnectionPool(host='localhost', port=56274): Max retries exceeded with url: /session/9f5d31ea683feeecd6dac9d04c694c0c/url (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000266F42DC640>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-08-05 13:50:35,114 - INFO - Processing student 5/147: 1103775
2025-08-05 13:50:35,114 - INFO - Processing student: 1103775 - Fifield,Megan Esme - PCEnvMgmt
2025-08-05 13:50:39,190 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000266F42DC2B0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/9f5d31ea683feeecd6dac9d04c694c0c/url
