import time
import logging
import openpyxl
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.wait import WebDriverWait
from selenium.webdriver.chrome.options import Options

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_complete_process():
    """Test the complete supervisor update process from start to finish"""
    
    # Load first student
    try:
        wb = openpyxl.load_workbook('student_list.xlsx')
        ws = wb.active
        first_row = list(ws.iter_rows(min_row=2, max_row=2, values_only=True))[0]
        student = {
            'id': str(first_row[0]),
            'name': first_row[1],
            'short_desc': first_row[6]
        }
        logger.info(f"Testing complete process with: {student['id']} - {student['short_desc']}")
    except Exception as e:
        logger.error(f"Failed to load student data: {e}")
        return
    
    # Setup Chrome driver
    chrome_options = Options()
    chrome_options.add_argument("--start-maximized")
    driver = webdriver.Chrome(options=chrome_options)
    wait = WebDriverWait(driver, 10)
    
    try:
        # Step 1: Navigate to system
        url = "https://lucas.lincoln.ac.nz/psc/ps/EMPLOYEE/SA/c/NUI_FRAMEWORK.PT_AGSTARTPAGE_NUI.GBL?CONTEXTIDPARAMS=TEMPLATE_ID%3aPTPPNAVCOL&scname=ADMN_RECORDS_AND_ENROLMENT&PanelCollapsible=Y&PTPPB_GROUPLET_ID=LU_RECORDS_AND_ENROLMENT&CRefName=LU_RECORDS_AND_ENROLMENT_TILE"
        driver.get(url)
        logger.info("✓ Step 1: Navigated to system")
        
        input("Please login and press Enter to continue...")
        
        # Step 2: Switch to frame and search
        try:
            driver.switch_to.frame(0)
            logger.info("✓ Step 2: Switched to frame")
        except:
            logger.info("Step 2: No frame to switch to")
        
        # Step 3: Enter student ID
        search_field = wait.until(EC.element_to_be_clickable((By.ID, "STDNT_CAR_SRCH_EMPLID")))
        search_field.clear()
        search_field.send_keys(student['id'])
        logger.info(f"✓ Step 3: Entered student ID: {student['id']}")
        
        # Step 4: Click search button
        search_btn = wait.until(EC.element_to_be_clickable((By.ID, "PTS_CFG_CL_WRK_PTS_SRCH_BTN")))
        search_btn.click()
        logger.info("✓ Step 4: Clicked search button")
        
        time.sleep(3)
        
        # Step 5: Handle search results and select correct row
        logger.info("Step 5: Handling search results...")
        result_rows = driver.find_elements(By.CSS_SELECTOR, "[id*='PTS_CFG_CL_RSLT_NUI_SRCH3']")
        logger.info(f"Found {len(result_rows)} result rows")
        
        if len(result_rows) == 1:
            result_rows[0].click()
            logger.info("✓ Step 5: Clicked single result")
        elif len(result_rows) > 1:
            # Find matching program
            for i, row in enumerate(result_rows):
                if student['short_desc'].upper() in row.text.upper():
                    row.click()
                    logger.info(f"✓ Step 5: Clicked matching row {i+1}")
                    break
        else:
            logger.error("✗ Step 5: No search results found")
            return
        
        time.sleep(3)
        
        # Step 6: Click new record button
        logger.info("Step 6: Looking for new record button...")
        new_btn = wait.until(EC.element_to_be_clickable((By.ID, "SSR_ACDPRG_AUS$new$0$$0")))
        new_btn.click()
        time.sleep(2)
        logger.info("✓ Step 6: Clicked new record button")
        
        # Step 7: Fill Program Action
        logger.info("Step 7: Filling Program Action...")
        prog_action = wait.until(EC.element_to_be_clickable((By.ID, "ACAD_PROG_PROG_ACTION$0")))
        prog_action.clear()
        prog_action.send_keys("Data")
        logger.info("✓ Step 7: Entered 'Data' in Program Action")
        
        # Step 8: Fill Program Reason
        logger.info("Step 8: Filling Program Reason...")
        prog_reason = driver.find_element(By.ID, "ACAD_PROG_PROG_REASON$0")
        prog_reason.clear()
        prog_reason.send_keys("PGRD")
        logger.info("✓ Step 8: Entered 'PGRD' in Program Reason")
        
        # Step 9: Fill Supervisor field
        logger.info("Step 9: Looking for supervisor field...")
        supervisor_field = None
        supervisor_field_ids = [
            "LU_SUPLU_TBL_LU_SUPERVISOR$0",
            "LU_SUP_LU_TBL_LU_SUPERVISOR$0",
            "LU_SUPERVISOR$0",
            "SUPERVISOR$0"
        ]
        
        for field_id in supervisor_field_ids:
            try:
                supervisor_field = driver.find_element(By.ID, field_id)
                logger.info(f"✓ Step 9: Found supervisor field: {field_id}")
                break
            except:
                continue
        
        if supervisor_field:
            supervisor_field.clear()
            supervisor_field.send_keys("8000401")
            logger.info("✓ Step 9: Entered supervisor ID: 8000401")
        else:
            logger.error("✗ Step 9: Could not find supervisor field")
            
            # Show all available input fields for debugging
            all_inputs = driver.find_elements(By.TAG_NAME, "input")
            logger.info("Available input fields:")
            for i, inp in enumerate(all_inputs):
                inp_id = inp.get_attribute('id')
                inp_type = inp.get_attribute('type')
                if inp_id and 'supervisor' in inp_id.lower():
                    logger.info(f"  Input {i}: {inp_id} (type: {inp_type})")
            return
        
        # Step 10: Ask before saving
        save_choice = input(f"\nReady to save changes for {student['name']} ({student['id']})?\nProgram Action: Data\nProgram Reason: PGRD\nSupervisor: 8000401\n\nSave? (y/n): ")
        
        if save_choice.lower() == 'y':
            logger.info("Step 10: Saving changes...")
            save_btn = driver.find_element(By.ID, "#ICSave")
            save_btn.click()
            logger.info("✓ Step 10: Clicked save button")
            
            # Wait and check for errors
            time.sleep(5)
            
            # Check for error messages
            try:
                error_elements = driver.find_elements(By.CSS_SELECTOR, ".PSERROR, .PSWARNING, .PSMSGDISPLAY")
                if error_elements:
                    error_text = " | ".join([elem.text for elem in error_elements if elem.text.strip()])
                    logger.error(f"✗ Error after save: {error_text}")
                else:
                    logger.info("✓ No error messages detected - save appears successful")
            except:
                logger.info("Could not check for error messages")
            
            logger.info("✓ Complete process finished successfully!")
        else:
            logger.info("Save cancelled by user")
        
    except Exception as e:
        logger.error(f"Error during complete process test: {e}")
        
    finally:
        input("Press Enter to close browser...")
        driver.quit()

if __name__ == "__main__":
    test_complete_process()
