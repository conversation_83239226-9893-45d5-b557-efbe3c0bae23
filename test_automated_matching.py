import time
import logging
import openpyxl
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.wait import WebDriverWait
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import NoSuchElementException

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_automated_matching():
    """Test the automated matching functionality with multiple students"""
    
    # Load students from Excel
    try:
        wb = openpyxl.load_workbook('student_list.xlsx')
        ws = wb.active
        
        # Get first few students from different programs
        students = []
        seen_programs = set()
        
        for row in ws.iter_rows(min_row=2, values_only=True):
            if row[0] and row[6]:  # ID and Short Desc exist
                short_desc = row[6]
                if short_desc not in seen_programs or len(students) < 5:
                    student = {
                        'id': str(row[0]),
                        'name': row[1],
                        'short_desc': short_desc
                    }
                    students.append(student)
                    seen_programs.add(short_desc)
                    
                    if len(students) >= 5:  # Test with 5 students max
                        break
        
        logger.info(f"Testing with {len(students)} students from different programs")
        for i, student in enumerate(students):
            logger.info(f"Student {i+1}: {student['id']} - {student['short_desc']}")
        
    except Exception as e:
        logger.error(f"Failed to load student data: {e}")
        return
    
    # Setup Chrome driver
    chrome_options = Options()
    chrome_options.add_argument("--start-maximized")
    driver = webdriver.Chrome(options=chrome_options)
    wait = WebDriverWait(driver, 10)
    
    try:
        # Navigate to system
        url = "https://lucas.lincoln.ac.nz/psc/ps/EMPLOYEE/SA/c/NUI_FRAMEWORK.PT_AGSTARTPAGE_NUI.GBL?CONTEXTIDPARAMS=TEMPLATE_ID%3aPTPPNAVCOL&scname=ADMN_RECORDS_AND_ENROLMENT&PanelCollapsible=Y&PTPPB_GROUPLET_ID=LU_RECORDS_AND_ENROLMENT&CRefName=LU_RECORDS_AND_ENROLMENT_TILE"
        driver.get(url)
        logger.info("✓ Navigated to system URL")
        
        # Wait for user to login
        input("Please login manually if required, then press Enter to continue...")
        
        # Test each student
        for i, student in enumerate(students):
            logger.info(f"\n{'='*60}")
            logger.info(f"Testing student {i+1}/{len(students)}: {student['id']} - {student['short_desc']}")
            logger.info(f"{'='*60}")
            
            try:
                # Switch to frame
                try:
                    driver.switch_to.frame(0)
                except:
                    pass
                
                # Search for student
                search_field = wait.until(EC.element_to_be_clickable((By.ID, "STDNT_CAR_SRCH_EMPLID")))
                search_field.clear()
                search_field.send_keys(student['id'])
                logger.info(f"✓ Entered student ID: {student['id']}")
                
                # Click search
                search_btn = wait.until(EC.element_to_be_clickable((By.ID, "PTS_CFG_CL_WRK_PTS_SRCH_BTN")))
                search_btn.click()
                logger.info("✓ Clicked search button")
                
                # Wait for results
                time.sleep(3)
                
                # Test automated matching
                success = test_result_matching(driver, student['short_desc'])
                
                if success:
                    logger.info(f"✓ Successfully matched and selected for {student['id']}")
                else:
                    logger.warning(f"✗ Failed to match for {student['id']}")
                
                # Go back to search page for next student
                try:
                    driver.back()
                    time.sleep(2)
                except:
                    # If back doesn't work, navigate to search page again
                    driver.get(url)
                    time.sleep(3)
                
            except Exception as e:
                logger.error(f"Error testing student {student['id']}: {e}")
                continue
        
        logger.info(f"\n{'='*60}")
        logger.info("Automated matching test completed!")
        logger.info(f"{'='*60}")
        
    except Exception as e:
        logger.error(f"Error during testing: {e}")
        
    finally:
        input("Press Enter to close browser...")
        driver.quit()

def test_result_matching(driver, expected_short_desc):
    """Test the automated result matching logic"""
    try:
        # Look for results table
        table_selectors = [
            "table[id*='PTS_CFG_CL_RSLT']",
            "table[id*='RSLT']",
            "#PTS_CFG_CL_RSLT_NUI_SRCH3\\$scroll\\$0"
        ]
        
        results_table = None
        for selector in table_selectors:
            try:
                results_table = driver.find_element(By.CSS_SELECTOR, selector)
                logger.info(f"✓ Found results table with selector: {selector}")
                break
            except NoSuchElementException:
                continue
        
        if not results_table:
            logger.info("✓ No results table found - assuming single result (direct redirect)")
            return True
        
        logger.info("Multiple search results found - testing automated matching")
        
        # Find result rows
        row_selectors = [
            "tr[id*='PTS_CFG_CL_RSLT']",
            "tr[id*='RSLT']",
            "tbody tr"
        ]
        
        rows = []
        for selector in row_selectors:
            try:
                rows = driver.find_elements(By.CSS_SELECTOR, selector)
                if rows:
                    logger.info(f"✓ Found {len(rows)} rows with selector: {selector}")
                    break
            except:
                continue
        
        if not rows:
            logger.warning("✗ No result rows found")
            return False
        
        # Print all rows for debugging
        logger.info("Available rows:")
        for i, row in enumerate(rows):
            row_text = row.text.strip()
            logger.info(f"  Row {i}: {row_text}")
        
        # Method 1: Search for exact match in row text
        matching_row = None
        for i, row in enumerate(rows):
            try:
                row_text = row.text.strip()
                
                # Check if the expected short description is in this row
                if expected_short_desc in row_text:
                    matching_row = row
                    logger.info(f"✓ Found matching row {i} using text search")
                    break
                    
            except Exception as e:
                logger.warning(f"Error processing row {i}: {e}")
                continue
        
        # Method 2: If no exact match, try cell-by-cell search
        if not matching_row:
            logger.info("Trying cell-by-cell search...")
            for i, row in enumerate(rows):
                try:
                    cells = row.find_elements(By.TAG_NAME, "td")
                    for j, cell in enumerate(cells):
                        cell_text = cell.text.strip()
                        if expected_short_desc in cell_text:
                            matching_row = row
                            logger.info(f"✓ Found matching row {i}, cell {j} using cell search")
                            break
                    if matching_row:
                        break
                except Exception as e:
                    logger.warning(f"Error processing row {i} cells: {e}")
                    continue
        
        if matching_row:
            # Click on the matching row
            matching_row.click()
            time.sleep(2)
            logger.info(f"✓ Successfully clicked matching row")
            return True
        else:
            logger.warning(f"✗ No matching row found for: {expected_short_desc}")
            return False
            
    except Exception as e:
        logger.error(f"Error in result matching: {e}")
        return False

if __name__ == "__main__":
    test_automated_matching()
