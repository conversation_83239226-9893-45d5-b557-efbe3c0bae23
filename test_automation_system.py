"""
Test Suite for Student Supervisor Automation System

This module contains comprehensive tests for the refactored automation system.
Run these tests to verify functionality before using the system.

Usage:
    python test_automation_system.py
"""

import unittest
import tempfile
import json
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock
import openpyxl

from student_supervisor_automation import (
    StudentRecord,
    ProcessingResult,
    AutomationLogger,
    StudentSupervisorAutomation
)


class TestStudentRecord(unittest.TestCase):
    """Test the StudentRecord data class"""
    
    def test_student_record_creation(self):
        """Test creating a valid student record"""
        student = StudentRecord(
            id="12345",
            name="<PERSON>",
            career="Undergraduate",
            eff_date="2024-01-01",
            status="Active",
            acad_prog="COMP",
            short_desc="Computer Science"
        )
        
        self.assertEqual(student.id, "12345")
        self.assertEqual(student.name, "<PERSON>")
        self.assertEqual(student.short_desc, "Computer Science")
    
    def test_student_record_data_cleaning(self):
        """Test data cleaning in student record"""
        student = StudentRecord(
            id=12345,  # Integer should be converted to string
            name="  <PERSON>  ",  # Whitespace should be stripped
            career="",
            eff_date="",
            status="",
            acad_prog="",
            short_desc="  Computer Science  "
        )
        
        self.assertEqual(student.id, "12345")
        self.assertEqual(student.name, "John Doe")
        self.assertEqual(student.short_desc, "Computer Science")


class TestProcessingResult(unittest.TestCase):
    """Test the ProcessingResult data class"""
    
    def test_processing_result_creation(self):
        """Test creating a processing result"""
        from datetime import datetime
        
        result = ProcessingResult(
            success=True,
            message="Success",
            student_id="12345",
            timestamp=datetime.now()
        )
        
        self.assertTrue(result.success)
        self.assertEqual(result.message, "Success")
        self.assertEqual(result.student_id, "12345")
        self.assertIsNone(result.error_details)


class TestAutomationLogger(unittest.TestCase):
    """Test the AutomationLogger class"""
    
    def setUp(self):
        """Set up test environment"""
        self.temp_dir = tempfile.mkdtemp()
        
    def test_logger_initialization(self):
        """Test logger initialization"""
        with patch('student_supervisor_automation.Path.mkdir'):
            logger_instance = AutomationLogger("DEBUG")
            logger = logger_instance.get_logger()
            
            self.assertIsNotNone(logger)
            self.assertEqual(logger.name, "StudentAutomation")
    
    def test_log_levels(self):
        """Test different log levels"""
        with patch('student_supervisor_automation.Path.mkdir'):
            for level in ["DEBUG", "INFO", "WARNING", "ERROR"]:
                logger_instance = AutomationLogger(level)
                logger = logger_instance.get_logger()
                self.assertIsNotNone(logger)


class TestStudentSupervisorAutomation(unittest.TestCase):
    """Test the main automation class"""
    
    def setUp(self):
        """Set up test environment"""
        # Create a temporary Excel file for testing
        self.temp_excel = tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False)
        self.temp_excel_path = Path(self.temp_excel.name)
        
        # Create test Excel data
        wb = openpyxl.Workbook()
        ws = wb.active
        
        # Headers
        headers = ['id', 'name', 'career', 'eff_date', 'status', 'acad_prog', 'short_desc']
        for col, header in enumerate(headers, 1):
            ws.cell(row=1, column=col, value=header)
        
        # Test data
        test_data = [
            ['12345', 'John Doe', 'UG', '2024-01-01', 'Active', 'COMP', 'Computer Science'],
            ['67890', 'Jane Smith', 'PG', '2024-01-01', 'Active', 'MATH', 'Mathematics']
        ]
        
        for row, data in enumerate(test_data, 2):
            for col, value in enumerate(data, 1):
                ws.cell(row=row, column=col, value=value)
        
        wb.save(self.temp_excel_path)
        wb.close()
    
    def tearDown(self):
        """Clean up test environment"""
        if self.temp_excel_path.exists():
            self.temp_excel_path.unlink()
    
    @patch('student_supervisor_automation.AutomationLogger')
    def test_automation_initialization(self, mock_logger):
        """Test automation system initialization"""
        mock_logger_instance = Mock()
        mock_logger.return_value = mock_logger_instance
        mock_logger_instance.get_logger.return_value = Mock()
        
        automation = StudentSupervisorAutomation(
            excel_file=str(self.temp_excel_path),
            supervisor_id="8000401",
            timeout=10,
            log_level="INFO"
        )
        
        self.assertEqual(automation.supervisor_id, "8000401")
        self.assertEqual(automation.timeout, 10)
        self.assertEqual(automation.excel_file, self.temp_excel_path)
    
    @patch('student_supervisor_automation.AutomationLogger')
    def test_load_student_data(self, mock_logger):
        """Test loading student data from Excel"""
        mock_logger_instance = Mock()
        mock_logger.return_value = mock_logger_instance
        mock_logger_instance.get_logger.return_value = Mock()
        
        automation = StudentSupervisorAutomation(
            excel_file=str(self.temp_excel_path),
            log_level="INFO"
        )
        
        students = automation.load_student_data()
        
        self.assertEqual(len(students), 2)
        self.assertEqual(students[0].id, "12345")
        self.assertEqual(students[0].name, "John Doe")
        self.assertEqual(students[1].id, "67890")
        self.assertEqual(students[1].name, "Jane Smith")
    
    @patch('student_supervisor_automation.AutomationLogger')
    def test_validation_with_missing_file(self, mock_logger):
        """Test validation with missing Excel file"""
        mock_logger_instance = Mock()
        mock_logger.return_value = mock_logger_instance
        mock_logger_instance.get_logger.return_value = Mock()
        
        with self.assertRaises(FileNotFoundError):
            StudentSupervisorAutomation(
                excel_file="nonexistent_file.xlsx",
                log_level="INFO"
            )
    
    @patch('student_supervisor_automation.AutomationLogger')
    @patch('student_supervisor_automation.webdriver.Chrome')
    def test_setup_driver(self, mock_chrome, mock_logger):
        """Test WebDriver setup"""
        mock_logger_instance = Mock()
        mock_logger.return_value = mock_logger_instance
        mock_logger_instance.get_logger.return_value = Mock()
        
        mock_driver = Mock()
        mock_chrome.return_value = mock_driver
        
        automation = StudentSupervisorAutomation(
            excel_file=str(self.temp_excel_path),
            log_level="INFO"
        )
        
        result = automation.setup_driver()
        
        self.assertTrue(result)
        self.assertIsNotNone(automation.driver)
        self.assertIsNotNone(automation.wait)
    
    @patch('student_supervisor_automation.AutomationLogger')
    def test_cleanup_driver(self, mock_logger):
        """Test WebDriver cleanup"""
        mock_logger_instance = Mock()
        mock_logger.return_value = mock_logger_instance
        mock_logger_instance.get_logger.return_value = Mock()
        
        automation = StudentSupervisorAutomation(
            excel_file=str(self.temp_excel_path),
            log_level="INFO"
        )
        
        # Mock driver
        mock_driver = Mock()
        automation.driver = mock_driver
        
        automation.cleanup_driver()
        
        mock_driver.quit.assert_called_once()
        self.assertIsNone(automation.driver)
        self.assertIsNone(automation.wait)


class TestIntegration(unittest.TestCase):
    """Integration tests for the complete system"""
    
    def setUp(self):
        """Set up integration test environment"""
        # Create test Excel file
        self.temp_excel = tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False)
        self.temp_excel_path = Path(self.temp_excel.name)
        
        wb = openpyxl.Workbook()
        ws = wb.active
        
        # Headers
        headers = ['id', 'name', 'career', 'eff_date', 'status', 'acad_prog', 'short_desc']
        for col, header in enumerate(headers, 1):
            ws.cell(row=1, column=col, value=header)
        
        # Single test record
        test_data = ['12345', 'Test Student', 'UG', '2024-01-01', 'Active', 'COMP', 'Computer Science']
        for col, value in enumerate(test_data, 1):
            ws.cell(row=2, column=col, value=value)
        
        wb.save(self.temp_excel_path)
        wb.close()
    
    def tearDown(self):
        """Clean up integration test environment"""
        if self.temp_excel_path.exists():
            self.temp_excel_path.unlink()
    
    @patch('student_supervisor_automation.AutomationLogger')
    def test_complete_data_flow(self, mock_logger):
        """Test complete data flow without WebDriver"""
        mock_logger_instance = Mock()
        mock_logger.return_value = mock_logger_instance
        mock_logger_instance.get_logger.return_value = Mock()
        
        automation = StudentSupervisorAutomation(
            excel_file=str(self.temp_excel_path),
            log_level="INFO"
        )
        
        # Test data loading
        students = automation.load_student_data()
        self.assertEqual(len(students), 1)
        
        # Test results structure
        self.assertIn('processed', automation.results)
        self.assertIn('successful', automation.results)
        self.assertIn('failed', automation.results)
        self.assertIn('skipped', automation.results)
        self.assertIn('errors', automation.results)
        self.assertIn('processing_results', automation.results)


def run_tests():
    """Run all tests and provide a summary"""
    print("="*80)
    print("STUDENT SUPERVISOR AUTOMATION SYSTEM - TEST SUITE")
    print("="*80)
    
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test classes
    test_classes = [
        TestStudentRecord,
        TestProcessingResult,
        TestAutomationLogger,
        TestStudentSupervisorAutomation,
        TestIntegration
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # Print summary
    print("\n" + "="*80)
    print("TEST SUMMARY")
    print("="*80)
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    print(f"Success rate: {((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100):.1f}%")
    
    if result.failures:
        print(f"\nFailures ({len(result.failures)}):")
        for test, traceback in result.failures:
            print(f"  - {test}: {traceback.split('AssertionError:')[-1].strip()}")
    
    if result.errors:
        print(f"\nErrors ({len(result.errors)}):")
        for test, traceback in result.errors:
            print(f"  - {test}: {traceback.split('Exception:')[-1].strip()}")
    
    print("="*80)
    
    return result.wasSuccessful()


if __name__ == "__main__":
    success = run_tests()
    exit(0 if success else 1)
