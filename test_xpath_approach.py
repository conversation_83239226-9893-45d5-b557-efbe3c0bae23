import time
import logging
import openpyxl
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.wait import WebDriverWait
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.action_chains import ActionChains

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_xpath_approach():
    """Test the XPath approach for finding and clicking rows"""
    
    # Load first student
    try:
        wb = openpyxl.load_workbook('student_list.xlsx')
        ws = wb.active
        first_row = list(ws.iter_rows(min_row=2, max_row=2, values_only=True))[0]
        student = {
            'id': str(first_row[0]),
            'name': first_row[1],
            'short_desc': first_row[6]
        }
        logger.info(f"Testing with: {student['id']} - {student['short_desc']}")
    except Exception as e:
        logger.error(f"Failed to load student data: {e}")
        return
    
    # Setup Chrome driver
    chrome_options = Options()
    chrome_options.add_argument("--start-maximized")
    driver = webdriver.Chrome(options=chrome_options)
    wait = WebDriverWait(driver, 10)
    
    try:
        # Navigate and search
        url = "https://lucas.lincoln.ac.nz/psc/ps/EMPLOYEE/SA/c/NUI_FRAMEWORK.PT_AGSTARTPAGE_NUI.GBL?CONTEXTIDPARAMS=TEMPLATE_ID%3aPTPPNAVCOL&scname=ADMN_RECORDS_AND_ENROLMENT&PanelCollapsible=Y&PTPPB_GROUPLET_ID=LU_RECORDS_AND_ENROLMENT&CRefName=LU_RECORDS_AND_ENROLMENT_TILE"
        driver.get(url)
        
        input("Please login and press Enter to continue...")
        
        # Switch to frame and search
        try:
            driver.switch_to.frame(0)
        except:
            pass
        
        search_field = wait.until(EC.element_to_be_clickable((By.ID, "STDNT_CAR_SRCH_EMPLID")))
        search_field.clear()
        search_field.send_keys(student['id'])
        
        search_btn = wait.until(EC.element_to_be_clickable((By.ID, "PTS_CFG_CL_WRK_PTS_SRCH_BTN")))
        search_btn.click()
        
        time.sleep(3)
        
        # XPath approach: Find elements containing the target text
        logger.info(f"Using XPath to find elements containing: '{student['short_desc']}'")
        
        # Find all elements containing the target program description
        xpath_query = f"//*[contains(text(), '{student['short_desc']}')]"
        target_elements = driver.find_elements(By.XPATH, xpath_query)
        logger.info(f"Found {len(target_elements)} elements containing '{student['short_desc']}'")
        
        if not target_elements:
            logger.error("No elements found with XPath approach")
            return False
        
        # Try each element
        for i, element in enumerate(target_elements):
            logger.info(f"\nTrying element {i}:")
            logger.info(f"  Tag: {element.tag_name}")
            logger.info(f"  Text: {element.text}")
            logger.info(f"  ID: {element.get_attribute('id')}")
            logger.info(f"  Class: {element.get_attribute('class')}")
            
            # Strategy 1: Try to find and click the parent row
            try:
                parent_row = element.find_element(By.XPATH, "./ancestor::tr[1]")
                logger.info(f"  Found parent row: {parent_row.get_attribute('id')}")
                logger.info(f"  Parent row text: {parent_row.text[:100]}...")
                
                # Try clicking the parent row
                try:
                    logger.info("  Attempting direct click on parent row...")
                    parent_row.click()
                    logger.info("  ✓ Direct click successful!")
                    time.sleep(3)
                    
                    # Check if page changed
                    new_url = driver.current_url
                    logger.info(f"  New URL: {new_url}")
                    
                    if new_url != url:
                        logger.info("  ✓ Page changed - success!")
                        return True
                    else:
                        logger.info("  Page didn't change, trying next strategy...")
                        
                except Exception as e:
                    logger.info(f"  Direct click failed: {e}")
                    
                    # Try JavaScript click on parent row
                    try:
                        logger.info("  Trying JavaScript click on parent row...")
                        driver.execute_script("arguments[0].click();", parent_row)
                        logger.info("  ✓ JavaScript click successful!")
                        time.sleep(3)
                        
                        new_url = driver.current_url
                        if new_url != url:
                            logger.info("  ✓ Page changed - success!")
                            return True
                            
                    except Exception as e2:
                        logger.info(f"  JavaScript click failed: {e2}")
                        
                        # Try ActionChains on parent row
                        try:
                            logger.info("  Trying ActionChains on parent row...")
                            actions = ActionChains(driver)
                            actions.move_to_element(parent_row).click().perform()
                            logger.info("  ✓ ActionChains click successful!")
                            time.sleep(3)
                            
                            new_url = driver.current_url
                            if new_url != url:
                                logger.info("  ✓ Page changed - success!")
                                return True
                                
                        except Exception as e3:
                            logger.info(f"  ActionChains failed: {e3}")
                
            except Exception as e:
                logger.info(f"  No parent row found: {e}")
            
            # Strategy 2: Try clicking the element directly
            try:
                logger.info("  Trying direct click on element...")
                element.click()
                logger.info("  ✓ Element click successful!")
                time.sleep(3)
                
                new_url = driver.current_url
                if new_url != url:
                    logger.info("  ✓ Page changed - success!")
                    return True
                    
            except Exception as e:
                logger.info(f"  Element click failed: {e}")
            
            # Strategy 3: Try JavaScript click on element
            try:
                logger.info("  Trying JavaScript click on element...")
                driver.execute_script("arguments[0].click();", element)
                logger.info("  ✓ JavaScript element click successful!")
                time.sleep(3)
                
                new_url = driver.current_url
                if new_url != url:
                    logger.info("  ✓ Page changed - success!")
                    return True
                    
            except Exception as e:
                logger.info(f"  JavaScript element click failed: {e}")
        
        logger.error("All click strategies failed")
        return False
        
    except Exception as e:
        logger.error(f"Error during test: {e}")
        return False
        
    finally:
        input("Press Enter to close browser...")
        driver.quit()

if __name__ == "__main__":
    success = test_xpath_approach()
    if success:
        print("\n✓ XPath approach worked! The automation should now work correctly.")
    else:
        print("\n✗ XPath approach failed. May need manual intervention.")
