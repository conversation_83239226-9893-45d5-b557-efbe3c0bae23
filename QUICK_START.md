# Quick Start Guide - Student Supervisor Automation System

## 🚀 Getting Started in 5 Minutes

### 1. Prerequisites Check
- ✅ Python 3.8+ installed
- ✅ Google Chrome browser installed
- ✅ Excel file with student data ready

### 2. Installation
```bash
# Install dependencies
pip install -r requirements.txt
```

### 3. Prepare Your Data
Ensure your Excel file (`student_list.xlsx`) has these columns:
| Column | Description | Required |
|--------|-------------|----------|
| id | Student ID | ✅ Yes |
| name | Student Name | No |
| career | Career Type | No |
| eff_date | Effective Date | No |
| status | Student Status | No |
| acad_prog | Academic Program | No |
| short_desc | Program Description | ✅ Yes (for matching) |

### 4. Run the System
```bash
# Basic usage
python student_supervisor_automation.py

# With custom settings
python student_supervisor_automation.py --excel-file "my_students.xlsx" --supervisor-id "8000402"
```

### 5. Monitor Progress
- **IMPORTANT**: Be ready to sign in manually when prompted
- Watch console output for real-time progress
- Check `logs/` folder for detailed logs
- Respond to prompts when manual intervention is needed

## 🔐 Sign-In Process

**The system will automatically detect when sign-in is required and prompt you:**

1. When you see "SIGN-IN REQUIRED" message:
   - The browser window will show the sign-in page
   - Sign in manually using your credentials
   - Press Enter in the console when sign-in is complete

2. The system will verify successful sign-in and continue automatically

## 🔧 Common Configurations

### Different Excel File
```bash
python student_supervisor_automation.py --excel-file "path/to/your/file.xlsx"
```

### Different Supervisor ID
```bash
python student_supervisor_automation.py --supervisor-id "8000999"
```

### Debug Mode
```bash
python student_supervisor_automation.py --log-level DEBUG
```

### Longer Timeout (for slow connections)
```bash
python student_supervisor_automation.py --timeout 20
```

## 📊 Understanding Output

### Console Messages
- `✓` = Success
- `✗` = Error
- `Processing student X/Y` = Progress indicator

### Log Files (in `logs/` folder)
- `automation_YYYYMMDD_HHMMSS.log` = Complete log
- `automation_errors_YYYYMMDD_HHMMSS.log` = Errors only
- `automation_results_YYYYMMDD_HHMMSS.json` = Results data

### Summary Report
```
AUTOMATION SUMMARY
==================
Total processed: 10
Successful: 8
Failed: 2
Skipped: 0
Success rate: 80.0%
```

## 🚨 When Things Go Wrong

### Common Issues & Quick Fixes

#### "Excel file not found"
- Check file path and name
- Ensure file exists in the correct location

#### "Chrome driver failed"
- Update Chrome browser
- Restart the application

#### "Element not found"
- Website might have changed
- Try increasing timeout: `--timeout 20`

#### "No matching program found"
- Check `short_desc` column in Excel
- Verify program names match system exactly

### Interactive Error Handling
When errors occur, you'll see options:
- `(r)etry` = Try the operation again
- `(s)kip` = Skip this student and continue
- `(m)anual fix` = Pause for manual intervention
- `(q)uit` = Stop the automation

## 🧪 Testing Before Production

### Run Tests
```bash
python test_automation_system.py
```

### Test with Sample Data
1. Create a small Excel file with 1-2 students
2. Run the automation
3. Verify results before processing larger batches

## 📈 Best Practices

### Before Running
1. **Backup your data** - Always have backups
2. **Test with small batches** - Start with 5-10 students
3. **Check system access** - Ensure you can access the target system
4. **Close other browsers** - Avoid conflicts

### During Execution
1. **Stay nearby** - Be ready to respond to prompts
2. **Don't minimize browser** - Keep it visible
3. **Monitor logs** - Watch for warnings or errors
4. **Be patient** - Each student takes 30-60 seconds

### After Completion
1. **Review summary** - Check success rates
2. **Verify results** - Spot-check in the target system
3. **Archive logs** - Keep logs for future reference
4. **Document issues** - Note any problems for next time

## 🔍 Troubleshooting Commands

### Check System Status
```bash
# Detailed logging
python student_supervisor_automation.py --log-level DEBUG

# Test with single student
# (Edit Excel file to have only one row)
python student_supervisor_automation.py --log-level DEBUG
```

### View Recent Logs
```bash
# Windows
type logs\automation_*.log | findstr ERROR

# Linux/Mac
tail -f logs/automation_*.log | grep ERROR
```

## 📞 Getting Help

### Self-Help Resources
1. Check `DOCUMENTATION.md` for detailed information
2. Review error logs in `logs/` folder
3. Run tests: `python test_automation_system.py`
4. Try debug mode: `--log-level DEBUG`

### Information to Collect for Support
- Error messages from console
- Log files from `logs/` folder
- Excel file structure (headers)
- Chrome browser version
- Python version: `python --version`

## 🎯 Success Tips

### Maximize Success Rate
1. **Clean data** - Ensure Excel data is accurate
2. **Stable connection** - Use reliable internet
3. **Updated browser** - Keep Chrome current
4. **Proper timing** - Run during low-traffic hours
5. **Monitor progress** - Stay engaged during execution

### Batch Processing Strategy
1. **Start small** - Process 10-20 students first
2. **Learn patterns** - Note common issues
3. **Optimize data** - Fix recurring data problems
4. **Scale up** - Gradually increase batch sizes
5. **Schedule wisely** - Plan for system maintenance windows

---

## 🎉 You're Ready!

The system is designed to be robust and user-friendly. Start with a small test batch, monitor the results, and scale up as you become comfortable with the process.

**Remember**: The automation handles the repetitive work, but your oversight ensures quality results!
