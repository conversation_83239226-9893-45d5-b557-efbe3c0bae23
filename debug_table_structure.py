import time
import logging
import openpyxl
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.wait import WebDriverWait
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import NoSuchElementException

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def debug_table_structure():
    """Debug the table structure to understand how to click rows"""
    
    # Load first student
    try:
        wb = openpyxl.load_workbook('student_list.xlsx')
        ws = wb.active
        first_row = list(ws.iter_rows(min_row=2, max_row=2, values_only=True))[0]
        student = {
            'id': str(first_row[0]),
            'name': first_row[1],
            'short_desc': first_row[6]
        }
        logger.info(f"Testing with: {student['id']} - {student['short_desc']}")
    except Exception as e:
        logger.error(f"Failed to load student data: {e}")
        return
    
    # Setup Chrome driver
    chrome_options = Options()
    chrome_options.add_argument("--start-maximized")
    driver = webdriver.Chrome(options=chrome_options)
    wait = WebDriverWait(driver, 10)
    
    try:
        # Navigate and search
        url = "https://lucas.lincoln.ac.nz/psc/ps/EMPLOYEE/SA/c/NUI_FRAMEWORK.PT_AGSTARTPAGE_NUI.GBL?CONTEXTIDPARAMS=TEMPLATE_ID%3aPTPPNAVCOL&scname=ADMN_RECORDS_AND_ENROLMENT&PanelCollapsible=Y&PTPPB_GROUPLET_ID=LU_RECORDS_AND_ENROLMENT&CRefName=LU_RECORDS_AND_ENROLMENT_TILE"
        driver.get(url)
        
        input("Please login and press Enter to continue...")
        
        # Switch to frame and search
        try:
            driver.switch_to.frame(0)
        except:
            pass
        
        search_field = wait.until(EC.element_to_be_clickable((By.ID, "STDNT_CAR_SRCH_EMPLID")))
        search_field.clear()
        search_field.send_keys(student['id'])
        
        search_btn = wait.until(EC.element_to_be_clickable((By.ID, "PTS_CFG_CL_WRK_PTS_SRCH_BTN")))
        search_btn.click()
        
        time.sleep(3)
        
        print("\n" + "="*80)
        print("DETAILED TABLE STRUCTURE ANALYSIS")
        print("="*80)
        
        # Find all tables
        tables = driver.find_elements(By.TAG_NAME, "table")
        print(f"Found {len(tables)} tables on page")
        
        for i, table in enumerate(tables):
            table_text = table.text.strip()
            if "PCEnvMgmt" in table_text or "Academic Career" in table_text:
                print(f"\nTable {i} (contains our data):")
                print(f"Table ID: {table.get_attribute('id')}")
                print(f"Table Class: {table.get_attribute('class')}")
                
                # Find all rows in this table
                rows = table.find_elements(By.TAG_NAME, "tr")
                print(f"Rows in table: {len(rows)}")
                
                for j, row in enumerate(rows):
                    row_text = row.text.strip()
                    row_id = row.get_attribute('id')
                    row_class = row.get_attribute('class')
                    
                    print(f"\n  Row {j}:")
                    print(f"    ID: {row_id}")
                    print(f"    Class: {row_class}")
                    print(f"    Text: {row_text[:100]}...")
                    
                    # Check if this row contains our target program
                    if student['short_desc'] in row_text:
                        print(f"    *** THIS ROW CONTAINS '{student['short_desc']}' ***")
                        
                        # Try to find clickable elements in this row
                        clickable_elements = row.find_elements(By.CSS_SELECTOR, "a, button, input[type='button'], [onclick], [role='button']")
                        print(f"    Clickable elements in row: {len(clickable_elements)}")
                        
                        for k, elem in enumerate(clickable_elements):
                            print(f"      Clickable {k}: {elem.tag_name} - {elem.get_attribute('id')} - {elem.get_attribute('class')}")
                        
                        # Try different click strategies
                        print(f"\n    Testing click strategies for this row:")
                        
                        try:
                            # Strategy 1: Click the row itself
                            print("    Strategy 1: Clicking row directly...")
                            row.click()
                            print("    ✓ Row click successful!")
                            time.sleep(2)
                            
                            # Check if page changed
                            current_url = driver.current_url
                            print(f"    Current URL after click: {current_url}")
                            
                            break
                            
                        except Exception as e:
                            print(f"    ✗ Row click failed: {e}")
                            
                            # Strategy 2: Click first clickable element in row
                            if clickable_elements:
                                try:
                                    print("    Strategy 2: Clicking first clickable element...")
                                    clickable_elements[0].click()
                                    print("    ✓ Clickable element click successful!")
                                    time.sleep(2)
                                    break
                                except Exception as e2:
                                    print(f"    ✗ Clickable element click failed: {e2}")
                            
                            # Strategy 3: Use JavaScript click
                            try:
                                print("    Strategy 3: Using JavaScript click...")
                                driver.execute_script("arguments[0].click();", row)
                                print("    ✓ JavaScript click successful!")
                                time.sleep(2)
                                break
                            except Exception as e3:
                                print(f"    ✗ JavaScript click failed: {e3}")
                
                break
        
        print("\n" + "="*80)
        print("Analysis complete!")
        
    except Exception as e:
        logger.error(f"Error during analysis: {e}")
        
    finally:
        input("Press Enter to close browser...")
        driver.quit()

if __name__ == "__main__":
    debug_table_structure()
